import { useCallback, useEffect, useRef, useState } from "react";
import Player from "xgplayer/es/player";
import recordIcon from "@/Resources/player/record.png";
import recordingIcon from "@/Resources/player/recording.png";
import "./RecordPlugin.css"
import { PreloadImage } from "@/components/Image";
import { ICameraDetail } from "@/pages/IPC/IPC_APP/CameraDetail";
import { startRecord, stopRecord, StopRecordData } from "@/api/ipc";
import { IDualOptions, IMonitorPlayerOptions, splitURL } from "../../MonitorPlayer/MonitorPlayer";
import { getSystemType } from "@/utils/DeviceType";
import { Toast } from "@/components/Toast/manager";

interface IRecordPlugin {
  cameraRef: React.RefObject<Player | null>; // 修改为视频元素引用
  callback: (item: StopRecordData) => void;
  deviceType: 'pc' | 'mobile';
  setIsRecord: (v: boolean) => void;
  isRecord: boolean;
  cameraDetail?: ICameraDetail;
  isFull: boolean // 是否全屏播放
  isDashboard?: boolean
  options?: IMonitorPlayerOptions
  dualOptions?: IDualOptions
}

const RecordPlugin = (props: IRecordPlugin) => {
  const { callback, setIsRecord, isRecord, cameraDetail, isFull, isDashboard, options, deviceType, dualOptions, cameraRef } = props;
  const loopKey = useRef<NodeJS.Timeout | null>(null); // 循环调接口的时间器
  const [saveId, setSaveId] = useState<number>(0);
  const isAndroid = getSystemType() === 'android';
  // const isIos = getSystemType() === 'ios';

  const getDid = useCallback((cameraDetail: ICameraDetail | undefined) => {
    if (!cameraDetail) return ``;
    let did = cameraDetail.did
    if (!dualOptions) {
      did = `${did}_0`;
    } else {
      if (cameraRef.current) {
        console.log(cameraRef.current);
        const src = cameraRef.current.config.url; // 获取当前播放的视频源地址
        // 判断是哪个摄像头在播放视频，并设置对应的did值
        if (splitURL(dualOptions.urls.main) === src) {
          did = `${did}_0`;
        }

        if (splitURL(dualOptions.urls.secondary) === src) {
          did = `${did}_1`;
        }

        if (dualOptions.urls.third && splitURL(dualOptions.urls.third) === src) {
          did = `${did}_2`;
        }
      }
    }
    return did;
  }, [cameraRef, dualOptions])

  const cameraStartRecord = useCallback(async () => {
    if (!cameraDetail) {
      console.log(`没有摄像头详情,无法开始录制!`)
      setIsRecord(false);
      return;
    }

    const did = getDid(cameraDetail);
    if (!did || did === '') {
      console.log(`did为空,无法开始录制!`)
      setIsRecord(false);
      return;
    }
    const res = await startRecord({ camera_lens: did, id: 0 }).catch((e) => console.log('start record err:', e))
    if (res && res.code === 0) {

      // 检查是否返回了id
      if (!res.data || !res.data.id) {
        if (loopKey.current) {
          clearInterval(loopKey.current);
        }
        console.log(`没有返回id,无法开始录制!`)
        setIsRecord(false);
        return;
      }

      // 保存id，后续停止录制
      setSaveId(res.data.id);
      loopKey.current = setInterval(async () => {
        await startRecord({ camera_lens: did, id: res.data.id || 0 }).catch((e) => console.log('start record err:', e))
      }, 30000);
    }

    if (res && res.code !== 0) {
      Toast.show(res.result);
    }
  }, [cameraDetail, getDid, setIsRecord])

  const startRecording = useCallback(() => {
    console.log('开始录制！');
    setIsRecord(true);
    cameraStartRecord();
  }, [cameraStartRecord, setIsRecord])

  // 停止录制
  const stopRecording = useCallback(async () => {
    if (!cameraDetail) return;

    const did = getDid(cameraDetail); // 获取摄像机ID

    if (!did || did === '') return;

    const res = await stopRecord({ camera_lens: did, id: saveId }).catch((e) => console.log('stop record err:', e)).finally(() => {
      setIsRecord(false);
      clearInterval(loopKey.current!); // 清除循环请求
      setSaveId(0);
    });

    if (res && res.code === 0) {
      callback(res.data)
      setIsRecord(false);
      clearInterval(loopKey.current!); // 清除循环请求
      setSaveId(0);
    }

    if (res && res.code !== 0) {
      Toast.show(res.result);
    }
  }, [callback, cameraDetail, getDid, saveId, setIsRecord]);

  useEffect(() => {
    return () => {
      if (loopKey.current) {
        clearInterval(loopKey.current);
      }
    }
  }, [])

  return (
    <div className="record_container">
      {
        isRecord ? (<PreloadImage className={`iconBtn_container_img ${isFull ? 'full' : 'notFull'} ${options?.type}  ${isAndroid ? 'android' : ''} ${deviceType} ${isDashboard && !isFull ? 'dashboard' : ''}`} onClick={stopRecording} src={recordingIcon} alt="停止录制" />)
          :
          (<PreloadImage className={`iconBtn_container_img ${isFull ? 'full' : 'notFull'} ${options?.type}  ${isAndroid ? 'android' : ''} ${deviceType} ${isDashboard && !isFull ? 'dashboard' : ''}`} onClick={startRecording} src={recordIcon} alt="开始录制" />)
      }
    </div>
  );
};

export default RecordPlugin;