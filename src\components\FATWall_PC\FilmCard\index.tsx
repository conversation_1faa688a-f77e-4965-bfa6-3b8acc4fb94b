import { PreloadImage } from "@/components/Image";
import styles from './index.module.scss';
import TVFocusable, { FocusableElement } from "@/pages/FATWall/FATWall_TV/TVFocus";
import like_icon from "@/Resources/filmWall/like.png";
import placeholder‌_poster from '@/Resources/filmWall/placeholder‌_col.png';
import douBan_icon from '@/Resources/filmWall/douban_icon.png';

export interface IFilmCard {
  url: string;
  score: string;
  name: string;
  title: string;
  isDrama?: any;
  favourite: boolean;
  media_id?: number;
  classes?: string;
}

const FilmCard = (props: IFilmCard & {
  row: number;
  col: number;
  id: string;
} & { currentItemCallback?: (item: FocusableElement, e: KeyboardEvent) => void, onClick?: () => void, closeAutoFind?: { closeDirect: 'row' | 'col' } }) => {
  const { url, score, name, title, row, col, id, currentItemCallback, onClick, favourite, closeAutoFind } = props;
  return (
    <div className={styles.container} key={name}>
      <TVFocusable id={id} row={row} col={col} className={styles.img_container} currentItemCallback={currentItemCallback} onClick={onClick} closeAutoFind={closeAutoFind}>
        <div className={styles.img_content}>
          <PreloadImage src={url || placeholder‌_poster} alt="img" />
          <div className={styles.score}>
            {score === '暂无评分' ? <></> : <PreloadImage src={douBan_icon} alt='豆瓣icon' />}
            <span>{score}</span>
          </div>
          {
            favourite && (
              <div className={styles.favourite}>
                <PreloadImage src={like_icon} alt="like_img" />
              </div>
            )
          }
        </div>
      </TVFocusable>
      <div className={styles.title} title={title}>{title}</div>
    </div>
  )
}

export default FilmCard;