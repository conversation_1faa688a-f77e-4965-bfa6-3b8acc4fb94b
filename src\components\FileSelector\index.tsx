import styles from './index.module.scss';
import { useCallback, useEffect, useRef, useState } from 'react';
import file_dir_icon from '@/Resources/layout/library2.png';
import not_select from '@/Resources/macBackup/not_select.png';
import selected from '@/Resources/macBackup/selected.png';
import next from "@/Resources/modal/next.png";
import next_dark from "@/Resources/modal/next_dark.png";
import { PreloadImage } from '../Image';
import { useTheme } from '@/utils/themeDetector';
import content_null from '@/Resources/filmWall/content_null.png';
import content_null_dark from '@/Resources/filmWall/content_null_dark.png';
import { ApiResponse, DirectoryListResponse, FileItem } from '@/api/fatWall';
import { useControllableValue, useInViewport, useUpdateEffect } from 'ahooks';

export interface IFileDirBaseProps {
  id: string;
  dir_name: string;
  data_dir: string;
  isDirectory: boolean
}

interface IFileDirComponentProps extends IFileDirBaseProps {
  selectedValue: string,
  setSelectedValue: (v: string) => void,
  dirOnClick: (path: string, dir_name: string, callback: (d: FileItem[]) => void) => Promise<void | ApiResponse<DirectoryListResponse>>,
  web_alias_root: string,
  isPool: boolean,
  requestPageOpt: React.MutableRefObject<{
    size: number;
    token: string;
  }>,
  setCurrentFileDirList: React.Dispatch<React.SetStateAction<FileItem[]>>
  setHasMore: React.Dispatch<React.SetStateAction<boolean>>;
}

interface IFileSelectorUtilProps {
  data: {
    currentFileDirConfig: { dir_name: string; data_dir: string }; // 当前所处文件夹配置信息，包括目录名和数据路径，面包屑用来追加显示
    current_file_dir_list: IFileDirBaseProps[]; // 当前文件夹列表，接口刷新list来实现进入文件夹
    web_alias_root: string; // 根目录别名，用于拼接完整路径
  }
  // 外部传入的方法，用于处理文件夹点击事件、初始化数据和分页配置等操作，callback方法用来控制目前数据是直接覆盖还是追加到当前列表中，
  dirOnClick: (path: string, dir_name: string, callback: (d: FileItem[]) => void) => Promise<void | ApiResponse<DirectoryListResponse>>;

  // 用来刷新当前文件夹列表数据，用于进入新目录时更新显示内容，主要是用来callback时区分是追加还是覆盖，以及更新当前文件夹路径等操作。
  setCurrentFileDirList: React.Dispatch<React.SetStateAction<FileItem[]>>;
  initData: () => void; // 初始化数据到最上级存储

  // 分页配置，用于控制请求的起始位置和数量等参数
  requestPageOpt: React.MutableRefObject<{
    size: number;
    token: string;
  }>
  onChange: (string: string) => void // 父子组件共同控制的state
  getCurrentFileDirPath?: (s: string) => void; // 获取当前文件夹完整路径
}

interface IBreadcrumbItemProps {
  label: string;
  data_dir: string;
}

const FileDirComponent = (props: IFileDirComponentProps) => {
  const { id, dir_name, selectedValue, setSelectedValue, data_dir, isDirectory, dirOnClick,
    web_alias_root, isPool, requestPageOpt, setCurrentFileDirList, setHasMore } = props;

  const dir_onClick = useCallback(async () => {
    if (!isDirectory) return; // 非目录不响应点击事件
    const path = isPool ? `${web_alias_root}${data_dir}` : data_dir;

    // 点击新文件夹时重置分页配置和选中项
    requestPageOpt.current = { size: 20, token: '' };
    setSelectedValue('');
    await dirOnClick(path, dir_name, d => setCurrentFileDirList(d)).then((res) => {
      if (res && res.code === 0) {
        if (res.data.page.size >= 20) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
      }
    });

  }, [data_dir, dirOnClick, dir_name, isDirectory, isPool, requestPageOpt, setCurrentFileDirList, setHasMore, setSelectedValue, web_alias_root])

  return (
    <div key={id} className={styles.file_dir_component_container}>
      <div className={styles.file_dir_component_left} onClick={dir_onClick}>
        <div className={styles.file_dir_component_left_img_container}>
          <PreloadImage className={styles.file_dir_component_icon} src={file_dir_icon} alt='file_dir' />
        </div>
        <div className={styles.file_dir_component_content}>
          <span>{dir_name}</span>
        </div>
      </div>
      <div className={styles.file_dir_component_right}>
        <PreloadImage onClick={() => { setSelectedValue(id); console.log(`选择该文件夹,文件夹路径为:${id}`) }}
          className={styles.file_dir_component_right_img} src={selectedValue === id ? selected : not_select} alt='file_dir' />
      </div>
    </div>
  )
}

const FileSelectorUtil = (props: IFileSelectorUtilProps) => {
  const { data, dirOnClick, initData, requestPageOpt, getCurrentFileDirPath, setCurrentFileDirList } = props;

  const [breadcrumbs, setBreadcrumbs] = useState<IBreadcrumbItemProps[]>([{ label: '内部存储', data_dir: '' }]);
  const [selectedValue, setSelectedValue] = useControllableValue<string>(props);
  const { isDarkMode } = useTheme();

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  // 面包屑更新逻辑
  useEffect(() => {
    if (!data.currentFileDirConfig.dir_name || !data.currentFileDirConfig.data_dir) return;

    if (data.currentFileDirConfig.dir_name !== '') {
      setBreadcrumbs(p => {
        return [...p, { label: data.currentFileDirConfig.dir_name, data_dir: data.currentFileDirConfig.data_dir }];
      });
    }
  }, [data.currentFileDirConfig.data_dir, data.currentFileDirConfig.dir_name])

  useEffect(() => {
    return () => {
      console.log('组件卸载,重置面包屑和选中项');
      setBreadcrumbs([{ label: '内部存储', data_dir: '' }]);
      setSelectedValue('');
    }
  }, [setSelectedValue])

  const selectDir = useCallback((selectedValue) => {
    setSelectedValue(p => {
      if (p === selectedValue) return '';

      return selectedValue;
    })
  }, [setSelectedValue])

  const breadcrumbOnClick = useCallback(async (item: IBreadcrumbItemProps) => {
    if (item.data_dir === '') {
      setBreadcrumbs([{ label: '内部存储', data_dir: '' }]);
      initData();
      return;
    }

    requestPageOpt.current = { size: 20, token: '' };
    const res = await dirOnClick(item.data_dir, item.label, d => setCurrentFileDirList(d));

    if (res && res.code === 0) {
      const bIndex = breadcrumbs.findIndex(i => i.data_dir === item.data_dir);
      if (bIndex !== -1) {
        setBreadcrumbs(p => {
          return p.slice(0, bIndex);
        });
      }
    }

  }, [breadcrumbs, dirOnClick, initData, requestPageOpt, setCurrentFileDirList])

  useUpdateEffect(() => {
    if (breadcrumbs && getCurrentFileDirPath) {
      console.log('需要更新当前目录')
      console.log('当前面包屑', breadcrumbs);
      if (breadcrumbs.length === 1) {
        getCurrentFileDirPath('');
      }
      getCurrentFileDirPath(breadcrumbs.slice(-1).map(it => it.data_dir).join('/'));
    }
  }, [breadcrumbs, getCurrentFileDirPath])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      // 下滑刷新加载
      if (!data.currentFileDirConfig.dir_name || !data.currentFileDirConfig.data_dir) return;
      setSelectedValue('');
      dirOnClick(
        data.currentFileDirConfig.data_dir,
        data.currentFileDirConfig.dir_name,
        d => setCurrentFileDirList(p => [...p, ...d])
      ).then((res) => {
        if (res && res.code === 0) {
          if (res.data.page.size >= 20) {
            setHasMore(true);
          } else {
            setHasMore(false);
          }
        }
      })
    }
  }, [inViewport, dirOnClick, data.currentFileDirConfig.data_dir, data.currentFileDirConfig.dir_name])

  return (
    <div className={styles.file_selector_container}>
      { /* 面包屑 */}
      <div className={styles.breadcrumb_container}>
        {
          breadcrumbs.length > 3 ? (
            <div className={styles.breadcrumb_content}>
              <div className={`${styles.breadcrumb_item}`}>
                ...
              </div>
              {
                breadcrumbs.slice(-2).map((item, index) => (
                  <div className={styles.breadcrumb_content} key={`${item.data_dir}_${index}`}>
                    {
                      <div className={styles.breadcrumb_next_container}>
                        <PreloadImage className={styles.breadcrumb_next_img} src={isDarkMode ? next_dark : next} alt='arrow' />
                      </div>
                    }
                    <div onClick={() => breadcrumbOnClick(item)}
                      className={`${styles.breadcrumb_item} ${(data.currentFileDirConfig.dir_name === '' && item.data_dir === '') || data.currentFileDirConfig.dir_name === item.label ? styles.breadcrumb_item_active : ''}`}>
                      {item.label}
                    </div>
                  </div>
                ))
              }
            </div>
          ) : breadcrumbs.map((item, index) => (
            <div className={styles.breadcrumb_content} key={`${item.data_dir}_${index}`}>
              {
                index !== 0 && <div className={styles.breadcrumb_next_container}>
                  <PreloadImage className={styles.breadcrumb_next_img} src={isDarkMode ? next_dark : next} alt='arrow' />
                </div>
              }
              <div onClick={() => breadcrumbOnClick(item)}
                className={`${styles.breadcrumb_item} ${(data.currentFileDirConfig.dir_name === '' && item.data_dir === '') || data.currentFileDirConfig.dir_name === item.label ? styles.breadcrumb_item_active : ''}`}>
                {item.label}
              </div>
            </div>
          ))
        }
      </div>
      <div className={styles.file_selector_content}>
        {/* 文件列表 */}
        {
          data.current_file_dir_list.length > 0 ? data.current_file_dir_list.every(it => !it.isDirectory) ?
            <div className={styles.file_selector_empty}>
              <PreloadImage className={styles.file_selector_empty_img} src={isDarkMode ? content_null_dark : content_null} alt='content_null' />
              <span className={styles.file_selector_empty_span}>路径下没有文件夹</span>
            </div>
            : data.current_file_dir_list.map((item) => {
              if (!item.isDirectory) {
                return null
              }
              return (
                <FileDirComponent setHasMore={setHasMore} requestPageOpt={requestPageOpt} {...item} key={item.id} isPool={data.currentFileDirConfig.dir_name === ''} selectedValue={selectedValue}
                  setSelectedValue={selectDir} dirOnClick={dirOnClick} web_alias_root={data.web_alias_root} setCurrentFileDirList={setCurrentFileDirList} />
              )
            }) : <div className={styles.file_selector_empty}>
            <PreloadImage className={styles.file_selector_empty_img} src={isDarkMode ? content_null_dark : content_null} alt='content_null' />
            <span className={styles.file_selector_empty_span}>路径下没有文件夹</span>
          </div>
        }

        {
          hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
        }
      </div>
    </div>
  )
}

export default FileSelectorUtil;
