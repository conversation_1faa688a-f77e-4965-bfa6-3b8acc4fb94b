import { ComponentType, FC, useEffect, useMemo, useRef, useState } from "react";
import { getWebDavInfo } from "@/utils/DeviceType";
import placeholder‌_poster from '@/Resources/filmWall/placeholder‌_row.png';
import XMLoading from "../XMLoading";
import { Skeleton } from "antd-mobile";

interface BaseImageProps {
  src: string;
  alt?: string;
  placeholder?: React.ReactNode;
  onClick?: (v?: any) => void
  style?: React.CSSProperties;
  className?: string;
  needHeader?: boolean;
  errorImage?: string; // 自定义错误图片
  onError?: () => void; // 错误回调
  loadingType?: 'Skeleton' | 'Spinner'; // 加载类型
}

interface PreloadStatus {
  isLoading: boolean;
  hasError: boolean;
}

type PreloadImageProps = BaseImageProps & PreloadStatus;

const withImagePreloader = <P extends BaseImageProps>(
  WrappedComponent: ComponentType<P>
): FC<BaseImageProps> => {
  return (props: BaseImageProps) => {
    const [loadStatus, setLoadStatus] = useState<boolean>(true);
    const [errorStatus, setErrorStatus] = useState<boolean>(false);
    const [displayUrl, setDisplayUrl] = useState<string | null>(null);
    const activeBlobUrl = useRef<string | null>(null); // 用于跟踪当前的 Blob URL

    // 获取 WebDAV 信息用于请求头
    const webDAVParams = getWebDavInfo();
    const headersParams = useMemo(() => {
      return {
        'Depth': '1',
        'Authorization': `Basic ${btoa(`${webDAVParams.username}:${webDAVParams.password}`)}`,
        'Content-Type': 'application/xml',
      }
    }, [webDAVParams.password, webDAVParams.username])

    const { src, needHeader, onError } = props;

    useEffect(() => {
      let isActive = true;

      const initImage = (url: string) => {
        const img = new Image();
        img.src = url;
        img.onload = () => {
          if (img.naturalWidth === 0 || img.naturalHeight === 0) {
            throw new Error('Image loaded but dimensions are zero');
          }

          if (isActive) {
            setDisplayUrl(url); // 设置显示的图片URL
            setLoadStatus(false); // 加载完成
            setErrorStatus(false); // 重置错误状态
          }
        };

        img.onerror = () => {
          if (isActive) {
            setLoadStatus(false); // 停止加载状态
            setErrorStatus(true); // 设置错误状态
            onError?.();
          }
        }
      }

      const loadImage = async () => {
        const imageUrl = src;

        // 清理之前的 Blob URL
        if (activeBlobUrl.current) {
          URL.revokeObjectURL(activeBlobUrl.current);
          activeBlobUrl.current = null;
        }

        try {
          // 仅当需要自定义请求头时才使用 fetch
          if (needHeader) {
            const response = await fetch(imageUrl, {
              headers: headersParams,
              mode: 'cors'
            });

            if (!response.ok) {
              throw new Error(`HTTP ${response.status} - Image load failed`);
            }

            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob); // 创建blob url
            activeBlobUrl.current = blobUrl;

            initImage(blobUrl);
            return;
          }

          initImage(imageUrl);
        } catch (error) {
          if (isActive) {
            setLoadStatus(false); // 停止加载状态
            setErrorStatus(true); // 设置错误状态
            onError?.();
          }
        }
      };

      // 初始化加载
      setLoadStatus(true);
      setErrorStatus(false);

      loadImage();

      return () => {
        isActive = false;
        // 保留 Blob URL 用于显示
      };
    }, [headersParams, needHeader, onError, src]);

    // 清理函数
    useEffect(() => {
      return () => {
        // 组件卸载时清理 Blob URL
        if (activeBlobUrl.current) {
          URL.revokeObjectURL(activeBlobUrl.current);
        }
      };
    }, []);

    // 传递增强后的props
    return <WrappedComponent
      {...(props as P)}
      src={displayUrl || props.src}
      isLoading={loadStatus} // 是否加载中
      hasError={errorStatus} // 是否加载失败
    />
  };
};

// Error 组件
const ErrorImage: FC<PreloadImageProps> = ({ className, style, onClick, errorImage }) => (
  <img src={errorImage || placeholder‌_poster} alt="error" onClick={onClick} className={className}
    style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      ...style
    }}
  />
)

const ImageComponent = (props: PreloadImageProps) => {
  const { src, alt, isLoading, hasError, placeholder, onClick, style, className, loadingType } = props;
  const imgRef = useRef<HTMLImageElement>(null);

  // 懒加载观察器
  useEffect(() => {
    if (!imgRef.current) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.01 });

    observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, []);

  // 加载中组件
  const XMLoadingComponent = useMemo(() => (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', ...style }} className={className}>
      <XMLoading loading={true} size='medium' />
      {placeholder}
    </div>
  ), [className, placeholder, style]);

  // 骨架屏组件
  const SkeletonComponent = useMemo(() => {
    return <Skeleton animated style={{ width: '100%', height: '100%' }} />
  }, [])

  if (hasError) return <ErrorImage {...props} />

  if (isLoading) {
    switch (loadingType) {
      case 'Spinner':
        return XMLoadingComponent;
      case 'Skeleton':
        return SkeletonComponent;
      default:
        return <div className={className} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', ...style }}>
          {placeholder}
        </div>;
    }
  }

  return (
    <img
      ref={imgRef}
      className={className}
      onClick={onClick}
      src={src}
      alt={alt}
      style={{
        ...style,
        opacity: isLoading ? 0 : 1,
      }}
      loading="lazy"
      decoding="async"
    />
  );
}

export const PreloadImage = withImagePreloader(ImageComponent);