import { NavBar } from "antd-mobile"
import { CSSProperties, ReactNode } from "react";
import style from './index.module.scss';
import { useHistory } from "react-router-dom";
import { px2rem } from "@/utils/setRootFontSize";
import { PreloadImage } from "../Image";
import backIcon_light from "@/Resources/icon/backIcon_light.png";
import backIcon_dark from "@/Resources/icon/backIcon_dark.png";
interface INavBar {
  styles?: CSSProperties
  height?: string
  borderBottom?: string
  backIcon?: string
  backIconTheme?: 'light' | 'dark'
  title?: string
  subtitle?: string
  left?: ReactNode
  right?: ReactNode //right side 部分尽量使用preloadImage组件或者img标签 antd的image会导致图标排版不一致
  backText?: string
  onBack?: () => void
  notBack?: boolean
}

const NavigatorBar = (props: INavBar) => {
  const history = useHistory<any>();
  const { 
    styles, 
    height = px2rem("56px"), 
    borderBottom, 
    backIcon, 
    backIconTheme = 'light',
    title, 
    left, 
    right, 
    subtitle, 
    backText, 
    onBack = () => history.goBack(), 
    notBack 
  } = props;

  const getBackIcon = () => {
    if (backIcon) return backIcon;
    return backIconTheme === 'light' ? backIcon_light : backIcon_dark;
  };

  return (
    <div className={style.navigatorBar_container}>
      <NavBar 
        style={{ ...styles, '--height': height, '--border-bottom': borderBottom }} 
        backIcon={notBack ? null : <PreloadImage src={getBackIcon()} alt="back" />} 
        back={backText} 
        left={left} 
        right={right} 
        onBack={onBack}
      >
        <div className={style.navigatorBar_children}>
          <div className={style.navigatorBar_title}>
            {title}
          </div>
          <div className={style.navigatorBar_subtitle}>
            {subtitle}
          </div>
        </div>
      </NavBar>
    </div>
  )
}

export default NavigatorBar;