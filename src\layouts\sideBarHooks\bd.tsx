import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
// import { useTheme } from '@/utils/themeDetector';
import { PreloadImage } from '@/components/Image';
import useMenus, { IMenus } from '@/router/menus';
import { getBaiduUserInfo } from '@/api/nasDisk';
import { useUser } from '@/utils/UserContext';

import styles from '../Layout.module.scss';
import rightArrow from '@/Resources/file/rightArrow.png';

// 用户信息接口
interface IUserInfo {
  avatar: string;
  name: string;
  memberLevel: string;
  memberStatus: string;
}

const useBDSideBar = () => {
  // const { path } = useRouteMatch();
  const history = useHistory();
  // const { isDarkMode } = useTheme();
  const menus = useMenus();
  const { userInfo: globalUserInfo, setUserInfo: setGlobalUserInfo, isLoading, setIsLoading } = useUser();

  // 本地显示用的用户信息状态
  const [userInfo, setUserInfo] = useState<IUserInfo>({
    avatar: '',
    name: '加载中...',
    memberLevel: 'VIP',
    memberStatus: '正在获取会员信息...'
  });

  // 获取用户信息
  const fetchUserInfo = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await getBaiduUserInfo({
        action: 'uinfo'
      });

      if (response.errno === 0) {
        // 存储到全局Context - 映射字段名
        setGlobalUserInfo({
          netdisk_name: response.netdisk_name,
          baidu_name: response.baidu_name,
          used_space: response.used,
          total_space: response.total,
          vip_type: response.vip_type,
          nas_vip: response.nas_vip,
          avatar_url: response.avatar_url,
          uk: response.uk,
          token: response.token
        });
        
        // 格式化会员状态
        const getMemberStatus = () => {
          if (response.nas_vip === 1) {
            return '开通百度网盘NAS会员';
          } else if (response.vip_type > 0) {
            return `VIP${response.vip_type} 会员`;
          } else {
            return '普通用户';
          }
        };

        // 格式化会员等级
        const getMemberLevel = () => {
          if (response.nas_vip === 1) {
            return 'NAS VIP';
          } else if (response.vip_type > 0) {
            return `VIP${response.vip_type}`;
          } else {
            return '普通用户';
          }
        };

        // 更新本地显示用的用户信息
        setUserInfo({
          avatar: response.avatar_url ,
          name: response.netdisk_name || response.baidu_name,
          memberLevel: getMemberLevel(),
          memberStatus: getMemberStatus()
        });
      } else {
        console.error('获取用户信息失败:', response.errmsg);
        // 保持默认值
        setUserInfo(prev => ({
          ...prev,
          name: '获取失败',
          memberStatus: '无法获取会员信息'
        }));
      }
    } catch (error) {
      console.error('获取用户信息异常:', error);
      setUserInfo(prev => ({
        ...prev,
        name: '获取失败',
        memberStatus: '无法获取会员信息'
      }));
    } finally {
      setIsLoading(false);
    }
  }, [setGlobalUserInfo, setIsLoading]);

  // 从全局Context初始化本地显示状态
  useEffect(() => {
    if (globalUserInfo) {
      // 格式化会员状态
      const getMemberStatus = () => {
        if (globalUserInfo.nas_vip === 1) {
          return '续费百度网盘NAS会员';
        } else if (globalUserInfo.vip_type > 0) {
          return `VIP${globalUserInfo.vip_type} 会员`;
        } else {
          return '普通用户';
        }
      };

      // 格式化会员等级
      const getMemberLevel = () => {
        if (globalUserInfo.nas_vip === 1) {
          return 'NAS VIP';
        } else if (globalUserInfo.vip_type > 0) {
          return `VIP${globalUserInfo.vip_type}`;
        } else {
          return '普通用户';
        }
      };

      setUserInfo({
        avatar: globalUserInfo.avatar_url ,
        name: globalUserInfo.netdisk_name || globalUserInfo.baidu_name,
        memberLevel: getMemberLevel(),
        memberStatus: getMemberStatus()
      });
    }
  }, [globalUserInfo]);

  // 注释掉重复的用户信息获取逻辑，由父组件负责获取
  // useEffect(() => {
  //   if (!globalUserInfo) {
  //     fetchUserInfo();
  //   }
  // }, [fetchUserInfo, globalUserInfo]);

  // 处理菜单点击
  const handleMenuClick = useCallback((key: string) => {
    const item: IMenus | undefined = menus.find((it) => it.key === key);
    if (item) {
      history.push(item.path);
    }
  }, [history, menus]);

  // 处理用户信息点击
  const handleUserInfoClick = useCallback(() => {
    // 不再传递用户信息，因为已经存储在全局Context中
    history.push({ 
      pathname: '/baiduNetdisk_pc/mine', 
      state: { 
        title: '个人中心'
      } 
    });
    // console.log('点击用户信息');
  }, [history]);

  // 用户信息展示组件
  const userInfoComponent = useMemo(() => {
    return (
      <div className={styles.userInfoContainer} onClick={handleUserInfoClick}>
        <div className={styles.userAvatar}>
          <PreloadImage src={userInfo.avatar} alt="用户头像" />
        </div>
        <div className={styles.userDetails}>
          <div className={styles.userName}>{userInfo.name}</div>
          <div className={styles.memberStatus}>
            {userInfo.memberStatus}
            <span className={styles.memberBadge}>
              <PreloadImage src={rightArrow} alt="arrow" />
            </span>
          </div>
        </div>
      </div>
    );
  }, [userInfo, handleUserInfoClick]);

  // 网盘侧边栏底部组件
  const bdComponents = useMemo(() => {
    return (
      <div className={styles.bdSidebarBottom}>
        {userInfoComponent}
      </div>
    );
  }, [userInfoComponent]);

  return {
    bdComponents,
    handleMenuClick,
    userInfo,
    setUserInfo,
    fetchUserInfo,
    isLoading
  };
};

export default useBDSideBar;
