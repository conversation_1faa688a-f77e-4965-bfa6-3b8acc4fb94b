import styles from './index.module.scss';
import baseStyles from '../Recently/RecentlyPlay/index.module.scss';
import { PreloadImage } from '@/components/Image';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import refreshIcon from '@/Resources/layout/refreshBtn.png';
import refreshIcon_dark from '@/Resources/layout/refreshBtn_dark.png';
import FilmFilter from '../../../../components/FATWall_APP/FilmFilter';
import arrow from '@/Resources/layout/arrow.png';
import arrow_dark from '@/Resources/layout/arrow_dark.png';
import { useTheme } from '@/utils/themeDetector';
import FilterFilmCard from '../../../../components/FATWall_APP/FilterFilmCard';
import selected from "@/Resources/icon/selected.png";
import notSelect from "@/Resources/icon/not_select.png";
import { px2rem } from '@/utils/setRootFontSize';
import { useHistory } from 'react-router-dom';
import searchIcon from '@/Resources/filmWall/search.png';
import { modalShow } from '@/components/List';
import MatchCorrection from './MatchCorrection';
import MediaSearchModal from './MediaSearchModal';
import { defaultFiltersByPc, filterItemType, filterTypeList } from '@/components/FATWall_APP/FATWALL_CONST';
import { collect, getFilePath, getMediaListFromLib, markWatched, mediaDelete, mediaProps, move2trashbin, searchLocalMedia } from '@/api/fatWall';
import { handleFilter } from '../../FATWall_APP/All';
import { useEventListener, useInViewport, useRequest, useThrottleFn, useUpdateEffect } from 'ahooks';
import FATErrorComponents from '../Error';
import { Toast } from '@/components/Toast/manager';
import { useLibraryList } from '..';
import film_filter_up_icon from "@/Resources/filmWall/film_filter_up_icon.png";
import film_filter_down_icon from "@/Resources/filmWall/film_filter_down_icon.png";
import film_filter_up_icon_dark from "@/Resources/filmWall/film_filter_up_icon_dark.png";
import film_filter_down_icon_dark from "@/Resources/filmWall/film_filter_down_icon_dark.png";
import film_filter_normal from "@/Resources/filmWall/film_filter_normal.png";
import film_filter_normal_dark from "@/Resources/filmWall/film_filter_normal_dark.png";

export const defaultTypeList = [
  { key: 'all-classes', title: '全部' },
  { key: 'movie', title: '电影' },
  { key: 'tv', title: '电视剧' },
]

export interface selectFilmProps {
  label: string;
  score: number;
  time: string;
  cover: string;
  isLike: number;
  [key: string]: any; // 其他属性
}

// 搜索状态类型
type SearchStatus = 'idle' | 'loading' | 'success' | 'error' | 'empty';

// 搜索结果类型
interface SearchResult {
  id: string;
  title: string;
  year: string;
  country: string;
  genres: string;
  posterUrl: string;
  score: string;
  type: string;
  media_id: number;
  classes: string;
}

export const defaultPageParamPC = { offset: 0, limit: 21 };

const AllByDesktop = () => {
  const [filters, setFilters] = useState<{ [key: string]: string }>(defaultFiltersByPc);
  const [collapse, setCollapse] = useState<boolean>(false);
  const { isDarkMode } = useTheme();
  const [hoverKey, setHoverKey] = useState<string>(''); // 悬浮key
  const [selectList, setSelectList] = useState<selectFilmProps[]>([]); // 已选择list
  const [showMatchCorrection, setShowMatchCorrection] = useState<boolean>(false);
  // const [searchModal, setSearchModal] = useState<boolean>(false);
  const [showSearchModal, setShowSearchModal] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [searchStatus, setSearchStatus] = useState<SearchStatus>('idle');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchCategory, setSearchCategory] = useState<'全部' | '电影' | '电视剧'>('全部');
  const history = useHistory();
  // const { path } = useRouteMatch();

  const { runAsync: getMediasFromLib } = useRequest(getMediaListFromLib, { manual: true }); // 根据媒体库获取所有资源
  const { runAsync: collectFilmRun } = useRequest(collect, { manual: true }); // 收藏或取消收藏
  const { runAsync: markWatchedRun } = useRequest(markWatched, { manual: true }); // 标记已观看或未观看
  const { runAsync: m2tRun } = useRequest(move2trashbin, { manual: true }); // 删除文件
  const { runAsync: deleteRun } = useRequest(mediaDelete, { manual: true }); // 从媒体库移除
  const [medias, setMedias] = useState<(mediaProps)[]>([]);
  const [filterItem, setFilterItem] = useState<filterItemType>({ sort_type: 0, asc: 0 }); // 排序
  const filterItemRef = useRef<filterItemType>({ sort_type: 0, asc: 0 }); // 根据添加时间、评分排序筛选
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParamPC); // 分页参数
  const prevFilters = useRef<{ [key: string]: string }>(defaultFiltersByPc); // 记录之前的筛选条件，用于判断筛选条件是否变化

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  const [isError, setIsError] = useState<boolean>(false); // 查询是否出错，用于显示刷新按钮
  const libs = useLibraryList().libs;

  const containerRef = useRef<HTMLDivElement | null>(null);
  const [needSticky, setNeedSticky] = useState<boolean>(false); // 筛选栏是否需要吸顶

  const { run } = useThrottleFn((ev: Event) => {
    // 滚动回顶部的时候取消吸顶
    if (containerRef.current && containerRef.current.scrollTop === 0) {
      setNeedSticky(false);
      return;
    }
    // current target空的时候不加载
    if (!ev.currentTarget) return;

    needSticky && setCollapse(true); // 当筛选栏吸顶时才进行折叠筛选栏
  }, { wait: 100 });

  useEventListener('scrollend', run, { target: containerRef.current });

  // 媒体库数据
  const filterFilm = useMemo(() => {
    return medias.map((item) => {
      return { ...item, label: item.trans_name, score: item.score || 0, time: `${item.year}`, cover: item.poster.length > 0 ? item.poster[0] : '', isLike: item.favourite }
    })
  }, [medias])

  const initMediaInfo = useCallback(async (callback: (data: mediaProps[]) => void, filter?) => {
    const mediaRes = await getMediasFromLib({ lib_id: 0, filter: { ...pageOptRef.current, ...filterItemRef.current, ...filter } }).catch((e) => console.log('获取媒体库影视列表失败：', e));
    if (mediaRes && mediaRes.code === 0 && mediaRes.data) {
      if (mediaRes.data.count < pageOptRef.current.limit) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      callback(mediaRes.data.medias); // 根据是筛选还是改变页数更新状态
      setIsError(false);
      return;
    }
    setIsError(true);
  }, [getMediasFromLib])

  // 初始化数据
  useEffect(() => {
    initMediaInfo((data) => setMedias(data), handleFilter(defaultFiltersByPc));
  }, [initMediaInfo])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      pageOptRef.current = { ...pageOptRef.current, offset: pageOptRef.current.offset + pageOptRef.current.limit };
      initMediaInfo((data) => setMedias(p => [...p, ...data]), handleFilter(filters));
    }
  }, [inViewport, initMediaInfo])

  const getDataByFilters = useCallback((filters) => {
    const filter = handleFilter(filters); // 处理筛选条件
    // 筛选条件更新的时候，重置页数，重置是否还有更多数据
    setHasMore(false);
    pageOptRef.current = defaultPageParamPC; // 重置页数
    initMediaInfo((data) => setMedias(data), filter);
  }, [initMediaInfo])

  useUpdateEffect(() => {
    containerRef.current && containerRef.current.scrollTo({ top: 0, behavior: 'smooth' }); // 滚回顶部
    filterItemRef.current = { ...filterItemRef.current, sort_type: filterItem.sort_type, asc: filterItem.asc }; // 记录之前的筛选条件，用于判断筛选条件是否变化
    // 该变化应将分页重置
    setHasMore(false);
    pageOptRef.current = defaultPageParamPC;
    initMediaInfo((data) => setMedias(data), handleFilter(filters));
  }, [filterItem])

  useUpdateEffect(() => {
    prevFilters.current = { ...filters }; // 记录之前的筛选条件，用于判断筛选条件是否变化
  }, [filters])

  useUpdateEffect(() => {
    containerRef.current && containerRef.current.scrollTo({ top: 0, behavior: 'smooth' }); // 筛选变化的时候滚回顶部
    getDataByFilters(filters);
  }, [filters, getDataByFilters])

  // 选择回调
  const selectCallback = useCallback((type: string, key: string) => {
    setFilters((filter: any) => {
      let f = { ...filter };
      f[type] = key;
      return f;
    })
  }, [])

  const clearAndRefresh = useCallback(() => {
    containerRef.current && containerRef.current.scrollTo({ top: 0, behavior: 'smooth' }); // 滚回顶部

    setHoverKey('');
    setSelectList([]);
    setFilters(defaultFiltersByPc); // 重置筛选条件,同时会重置页数

    // 如果页面参数没有变化则手动加载数据
    if (prevFilters.current) {
      if (JSON.stringify(prevFilters.current) === JSON.stringify(defaultFiltersByPc)) {
        getDataByFilters(defaultFiltersByPc);
      }
    }
  }, [getDataByFilters])

  // 选中
  const clickCallback = useCallback((it: any) => {
    setSelectList((p: any[]) => {
      let pr = [...p];
      const isIncludes: boolean = pr.includes(it);
      if (isIncludes) {
        pr = pr.filter(item => item !== it);
      } else {
        pr.push(it);
      }
      return pr;
    })
  }, [])

  // 收藏
  const like = useCallback(async () => {
    const res = await collectFilmRun({ media_ids: selectList.map(it => it.media_id), favourite: 1 });
    if (res && res.code === 0) {
      Toast.show('收藏成功!');
      setSelectList([]); // 清空选中列表
      clearAndRefresh();

      return;
    }
    Toast.show('收藏失败，请稍后再试！');
  }, [collectFilmRun, clearAndRefresh, selectList])

  // 取消收藏
  const cancelLike = useCallback(async () => {
    const res = await collectFilmRun({ media_ids: selectList.map(it => it.media_id), favourite: 0 });
    if (res && res.code === 0) {
      Toast.show('取消收藏成功!');
      setSelectList([]); // 清空选中列表
      clearAndRefresh();

      return;
    }
    Toast.show('取消收藏失败，请稍后再试！');
  }, [collectFilmRun, clearAndRefresh, selectList])

  // 取消标记
  const cancelPlayed = useCallback(async () => {
    const res = await markWatchedRun({ media_ids: selectList.map(it => it.media_id), seen: 0 });
    if (res && res.code === 0) {
      Toast.show('取消标记成功!');
      setSelectList([]); // 清空选中列表
      clearAndRefresh();

      return;
    }
    Toast.show('标记失败，请稍后再试！');
  }, [markWatchedRun, clearAndRefresh, selectList])

  // 标记
  const played = useCallback(async () => {
    const res = await markWatchedRun({ media_ids: selectList.map(it => it.media_id), seen: 1 });
    if (res && res.code === 0) {
      Toast.show('标记成功!');
      setSelectList([]); // 清空选中列表
      clearAndRefresh();

      return;
    }
    Toast.show('标记失败，请稍后再试！');
  }, [markWatchedRun, clearAndRefresh, selectList])

  // 修正
  const edit = useCallback(() => {
    setShowMatchCorrection(true)
    console.log('修改成功!')
  }, [])

  const move2Trashbin = useCallback(async (m) => {
    const mediaIds = selectList.map(it => it.media_id);
    const res = await getFilePath({ media_ids: mediaIds, lib_id: 0 });
    if (res && res.code === 0) {
      const res1 = await m2tRun({ path: res.data.path });
      if (res1 && res1.code === 0) {
        Toast.show('删除成功!');
        setSelectList([]);
        m.destroy();
        clearAndRefresh();

        return;
      }
      Toast.show('删除失败，请稍后再试！');
    }
  }, [m2tRun, clearAndRefresh, selectList])

  const delFile = useCallback((modal) => {
    modalShow(`是否确定删除${selectList.length}个文件？`, <>删除的文件将移至"回收站"，保留30天</>, async (m) => {
      const res = await deleteRun({ media_ids: selectList.map(it => it.media_id), lib_id: 0 });
      if (res && res.code === 0) {
        Toast.show('删除成功!');
        setSelectList([]);
        m.destroy();
        modal.destroy();
        clearAndRefresh();

        return;
      }
      Toast.show('删除失败!');
    }, () => null, false, { position: 'center', okBtnText: '删除', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' } })
  }, [deleteRun, clearAndRefresh, selectList])

  // 删除
  const del = useCallback(() => {
    const m = modalShow('确认删除吗？', (
      <>
        <div className={styles.modal_button} onClick={() => delFile(m)}>仅从媒体库移除</div>
        <div className={styles.modal_button} onClick={() => move2Trashbin(m)}>删除文件</div>
      </>
    ), () => null, () => null, false, { okBtnStyle: { display: 'none' }, cancelBtnStyle: { width: px2rem('300px'), margin: 0 }, position: 'center' })
  }, [delFile, move2Trashbin])

  // 处理搜索
  const handleSearch = useCallback(async (keyword: string) => {
    if (keyword.trim()) {
      setSearchStatus('loading');

      try {
        // 根据搜索分类确定接口参数
        let classes = '';
        if (searchCategory === '电影') {
          classes = '电影';
        } else if (searchCategory === '电视剧') {
          classes = '电视剧';
        }

        const response = await searchLocalMedia({
          keyword: keyword.trim(),
          filter: {
            offset: 0,
            limit: 50,
            classes
          }
        });

        if (response.code === 0 && response.data) {
          const { medias, count } = response.data;

          if (count === 0) {
            setSearchStatus('empty');
            setSearchResults([]);
          } else {
            // 将接口返回的mediaProps数据转换为SearchResult格式
            const transformedResults: SearchResult[] = medias.map(media => ({
              id: media.media_id.toString(),
              title: media.trans_name || media.origin_name || media.other_name,
              year: media.year.toString(),
              country: media.origin_place,
              genres: media.kind.join(' / '),
              posterUrl: media.poster.length > 0 ? media.poster[0] : '',
              score: media.score ? media.score.toString() : '暂无评分',
              type: media.classes === '电视剧' ? '电视剧' : '电影',
              media_id: media.media_id,
              classes: media.classes
            }));

            setSearchResults(transformedResults);
            setSearchStatus('success');
          }
        } else {
          setSearchStatus('error');
          setSearchResults([]);
        }
      } catch (error) {
        console.error('搜索失败:', error);
        setSearchStatus('error');
        setSearchResults([]);
      }
    }
  }, [searchCategory]);

  // 处理搜索重试
  const handleSearchRetry = useCallback(() => {
    if (searchKeyword.trim()) {
      handleSearch(searchKeyword);
    }
  }, [searchKeyword, handleSearch]);

  // 处理分类切换
  const handleCategoryChange = useCallback((category: '全部' | '电影' | '电视剧') => {
    setSearchCategory(category);
  }, []);

  // 当搜索分类改变且有搜索关键词时，重新搜索
  useEffect(() => {
    if (searchKeyword.trim() && searchStatus === 'success') {
      handleSearch(searchKeyword);
    }
  }, [searchCategory]); // 只监听searchCategory变化

  // 处理搜索关键词变化
  const handleSearchKeywordChange = useCallback((keyword: string) => {
    setSearchKeyword(keyword);
  }, []);

  // 重置搜索状态
  const resetSearchStatus = useCallback(() => {
    setSearchStatus('idle');
    setSearchResults([]);
    setSearchKeyword('');
  }, []);

  // 搜索
  const search = useCallback(() => {
    // setSearchModal(false);
    setShowSearchModal(true)
    resetSearchStatus();
  }, [resetSearchStatus])

  const toDarmaOrMovie = useCallback((item: any) => {
    console.log(item);

    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: '0'
    });

    history.push(`/filmAndTelevisionWall_pc/all/videoDetails?${params.toString()}`);
  }, [history])

  // 处理搜索结果点击跳转
  const handleSearchItemClick = useCallback((item: any) => {
    console.log('PC端搜索结果点击项:', item);

    // 转换搜索结果数据格式，确保包含media_id
    const convertedItem = {
      label: item.label,
      score: item.core,
      time: item.time,
      cover: item.cover,
      isLike: item.isLike,
      classes: item.isDrama ? '电视剧' : '电影',
      media_id: item.media_id || parseInt(item.id) // 确保media_id存在
    };

    // 关闭搜索界面
    setShowSearchModal(false);
    resetSearchStatus();

    // 将参数拼接到URL上，以便webView能够正确获取
    const params = new URLSearchParams({
      classes: convertedItem.classes || '',
      media_id: convertedItem.media_id?.toString() || '0',
      lib_id: '0'
    });

    history.push(`/filmAndTelevisionWall_pc/all/videoDetails?${params.toString()}`);
  }, [history, setShowSearchModal, resetSearchStatus])

  useEffect(() => {
    // if (isError || medias.length === 0) {
    //   setCollapse(true); // 如果没有影片数据，则默认收起筛选条件
    // }

    // if (medias.length > 0 && pageOptRef.current.offset === 0) {
    //   setCollapse(false); // 如果有影片数据，则默认展开筛选条件
    // }
  }, [isError, medias.length])

  return (
    <div className={styles.container}>
      <div className={`${baseStyles.header} ${styles.header}`}>
        <div className={baseStyles.util_items} style={{ justifyContent: 'flex-start' }}>
          {
            defaultTypeList.map(it => (
              <div key={it.key} className={`${styles.tabs_item} ${filters['classes'] === it.key ? styles.selected : ''}`} onClick={() => selectCallback('classes', it.key)}>
                <span>{it.title}</span>
              </div>
            ))
          }
        </div>
        <div className={baseStyles.util_items}>
          {
            selectList.length === 0 ? (
              <>
                <PreloadImage src={searchIcon} alt="search" onClick={search} />
                <div className={baseStyles.operationItem} onClick={() => setCollapse((c: boolean) => !c)}>
                  <span>筛选</span>
                  <PreloadImage style={{ transform: !collapse ? 'rotate(0deg)' : 'rotate(180deg)' }} src={isDarkMode ? arrow_dark : arrow} alt='more' />
                </div>
                <div style={{ opacity: filterItem.sort_type ? '0.3' : '1' }} className={baseStyles.operationItem} onClick={() => setFilterItem(prev => { return { ...prev, sort_type: 0, asc: prev.sort_type === 0 ? prev.asc ? 0 : 1 : prev.asc } })}>
                  <span>添加时间</span>
                  {
                    isDarkMode ? (<PreloadImage src={filterItem.sort_type ? film_filter_normal_dark : filterItem.asc ? film_filter_up_icon_dark : film_filter_down_icon_dark} alt='time_select' />) :
                      (<PreloadImage src={filterItem.sort_type ? film_filter_normal : filterItem.asc ? film_filter_up_icon : film_filter_down_icon} alt='time_select' />)
                  }
                </div>
                <div style={{ opacity: filterItem.sort_type ? '1' : '0.3' }} className={baseStyles.operationItem} onClick={() => setFilterItem(prev => { return { ...prev, sort_type: 1, asc: prev.sort_type === 1 ? prev.asc ? 0 : 1 : prev.asc } })}>
                  <span>评分</span>
                  {
                    isDarkMode ? (<PreloadImage src={filterItem.sort_type ? filterItem.asc ? film_filter_up_icon_dark : film_filter_down_icon_dark : film_filter_normal_dark} alt='time_select' />) :
                      (<PreloadImage src={filterItem.sort_type ? filterItem.asc ? film_filter_up_icon : film_filter_down_icon : film_filter_normal} alt='score_select' />)
                  }
                </div>
                <PreloadImage src={isDarkMode ? refreshIcon_dark : refreshIcon} alt="refresh" onClick={clearAndRefresh} />
              </>
            ) : (
              <>
                {
                  selectList.every((it) => it.isLike) ? (
                    <div className={baseStyles.operationItem} onClick={cancelLike}>
                      <span>取消收藏</span>
                    </div>
                  ) : (
                    <div className={baseStyles.operationItem} onClick={like}>
                      <span>收藏</span>
                    </div>
                  )
                }
                {
                  selectList.every((it) => it.seen) ? (
                    <div className={baseStyles.operationItem} onClick={cancelPlayed}>
                      <span>取消已观看</span>
                    </div>
                  ) : (
                    <div className={baseStyles.operationItem} onClick={played}>
                      <span>已观看</span>
                    </div>
                  )
                }
                <div className={baseStyles.operationItem} onClick={edit}>
                  <span>修正匹配信息</span>
                </div>
                <div className={baseStyles.operationItem} style={{ color: 'rgba(255, 112, 113, 1)' }} onClick={del}>
                  <span>删除</span>
                </div>
              </>
            )
          }
        </div>
      </div>

      <div className={`${styles.content}`} ref={containerRef}>
        {/* 条件筛选 */}
        <FilmFilter filters={filters} stickyCallback={setNeedSticky} stickyValue={needSticky} filterTypeList={filterTypeList} controlFilters={setFilters} value={collapse} onChange={setCollapse} type='pc' isDisabled={selectList.length !== 0} />

        <div className={styles.films_card_error_container} style={{ "height": `calc(100% - 14px - ${collapse ? '40px' : px2rem('210px')})` } as React.CSSProperties}>
          <FATErrorComponents span={isError ? '获取失败' : '暂无内容'} canTry={isError} refresh={clearAndRefresh} show={isError || libs.length === 0 || filterFilm.length === 0} subSpan={isError ? undefined : libs.length > 0 && filterFilm.length === 0 ? '请在媒体库关联的文件夹中添加视频' : undefined}>

            {/* 影片渲染 */}
            <div className={styles.filter_films_container} style={{ marginTop: collapse ? 0 : px2rem('15px') }}>
              {
                filterFilm.map((item, index) => (
                  <div key={item.label + index} className={styles.film_card_container} style={{ background: selectList.includes(item) ? 'var(--fat-card-hover-bg)' : '' }}
                    onMouseLeave={() => setHoverKey('')} onMouseEnter={() => setHoverKey(item.label)} onClick={() => toDarmaOrMovie(item)}>

                    <FilterFilmCard type='pc' title={item.label} subtitle={item.time} score={item.score} cover={item.cover} isLike={item.isLike ? true : false} />
                    { /* 选中div */}
                    <div className={baseStyles.select_item} onClick={(e) => { e.stopPropagation(); clickCallback(item); }}
                      style={{ visibility: (selectList.includes(item) || hoverKey === item.label) ? 'visible' : 'hidden' }}>
                      {selectList.includes(item) ? <PreloadImage src={selected} alt="selected" /> : <PreloadImage src={notSelect} alt="notSelect" />}
                    </div>
                  </div>
                ))
              }
            </div>

            {
              hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
            }
          </FATErrorComponents>
        </div>
      </div>

      <MatchCorrection
        visible={showMatchCorrection}
        onClose={() => {
          setShowMatchCorrection(false);
          setSelectList([]);
        }}
        selectList={selectList}
        refresh={clearAndRefresh}
      />
      {/* 搜索 */}
      <MediaSearchModal
        visible={showSearchModal}
        searchStatus={searchStatus}
        searchResults={searchResults}
        searchKeyword={searchKeyword}
        searchCategory={searchCategory}
        onSearch={handleSearch}
        onSearchKeywordChange={handleSearchKeywordChange}
        onCategoryChange={handleCategoryChange}
        onRetry={handleSearchRetry}
        onResetStatus={resetSearchStatus}
        onItemClick={handleSearchItemClick}
        onClose={() => {
          setShowSearchModal(false);
          setSelectList([]);
          resetSearchStatus();
        }}
      />
    </div>
  )
}

export default AllByDesktop;