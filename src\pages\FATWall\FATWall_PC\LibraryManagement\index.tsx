import { useState, useEffect, useCallback, useRef } from 'react';
import styles from './index.module.scss';
import { PreloadImage } from '@/components/Image';
import refreshIcon from '@/Resources/layout/refreshBtn.png';
import refreshBtn_dark from '@/Resources/layout/refreshBtn_dark.png';
import addLib_dark from '@/Resources/icon/addLib_dark.png';
import addLib_light from '@/Resources/icon/addLib_light.png';
import { Modal, Button } from 'antd';
import { Toast } from '@/components/Toast/manager';
import request from '@/request';

import MediaLibraryTable, { IMediaLibrary } from './MediaLibraryTable';
import CreateLibraryModal from './CreateLibraryModal';
import { useTheme } from '@/utils/themeDetector';
import { useLibraryList } from '..';



interface ScanProgress {
    [libraryId: string]: number; // 0-100的进度百分比
}

// 新的媒体库数据结构接口
interface LibraryData {
    my_libs: {
        count: number;
        libs: IMediaLibrary[];
    };
    share2me: {
        count: number;
        libs: IMediaLibrary[];
    };
}



const LibraryManagement = () => {
    const { setLibs } = useLibraryList();
    // 合并后的媒体库数据状态
    const [libraryData, setLibraryData] = useState<LibraryData>({
        my_libs: { count: 0, libs: [] },
        share2me: { count: 0, libs: [] }
    });
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [editingLibrary, setEditingLibrary] = useState<IMediaLibrary | null>(null);

    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [deleteLibraryId, setDeleteLibraryId] = useState<string>('');
    const [deleteLibraryName, setDeleteLibraryName] = useState<string>('');
    const [deleteLibraryType, setDeleteLibraryType] = useState<'my_libs' | 'share2me'>('my_libs');
    const [scanProgress,] = useState<ScanProgress>({});
    const [, setLoading] = useState(false); // 手动控制loading状态
    const [, setIsPolling] = useState(false); // 标识是否正在轮询
    const { isDarkMode } = useTheme();
    const pollingTimerRef = useRef<NodeJS.Timeout | null>(null); // 轮询定时器引用
    const [completedScans, setCompletedScans] = useState<Set<string>>(new Set()); // 跟踪刚完成扫描的媒体库
    const completedScanTimersRef = useRef<Map<string, NodeJS.Timeout>>(new Map()); // 跟踪完成扫描的定时器
    const previousLibraryStatusRef = useRef<Map<string, string>>(new Map()); // 存储之前的扫描状态

    // 停止轮询
    const stopPolling = useCallback(() => {
        if (pollingTimerRef.current) {
            clearInterval(pollingTimerRef.current);
            pollingTimerRef.current = null;
        }
        setIsPolling(false); // 标记轮询已停止
    }, []);

    // 清理完成扫描的定时器
    const clearCompletedScanTimer = useCallback((libId: string) => {
        const timer = completedScanTimersRef.current.get(libId);
        if (timer) {
            clearTimeout(timer);
            completedScanTimersRef.current.delete(libId);
        }
    }, []);

    // 处理扫描完成状态
    const handleScanCompleted = useCallback((libId: string) => {
        setCompletedScans(prev => new Set(prev).add(libId));

        // 清理之前的定时器（如果存在）
        clearCompletedScanTimer(libId);

        // 3秒后移除完成状态
        const timer = setTimeout(() => {
            setCompletedScans(prev => {
                const newSet = new Set(prev);
                newSet.delete(libId);
                return newSet;
            });
            completedScanTimersRef.current.delete(libId);
        }, 3000);

        completedScanTimersRef.current.set(libId, timer);
    }, [clearCompletedScanTimer]);

    // 检查是否有正在扫描的媒体库
    const hasScanning = useCallback((libraries: IMediaLibrary[]) => {
        return libraries.some(lib => lib.scan_status === '扫描中');
    }, []);

    // 根据扫描状态生成状态文本
    // const getStatusText = useCallback((lib: any) => {
    //     const { scan_status, scan_percent, scan_error_code, lib_id } = lib;

    //     // 如果是刚完成扫描的媒体库，显示"扫描完成"
    //     if (completedScans.has(lib_id?.toString())) {
    //         return '扫描完成';
    //     }

    //     switch (scan_status) {
    //         case '扫描中':
    //             return `扫描中 ${Math.round(scan_percent || 0)}%`;
    //         case '扫描异常':
    //             return `扫描失败：${scan_error_code || '未知错误'}`;
    //         case '扫描完成':
    //         case '未开始':
    //         default:
    //             return '正常';
    //     }
    // }, [completedScans]);

    // 根据API数据格式更新媒体库数据的辅助函数
    const updateLibraryDataFromAPI = useCallback((apiData: any) => {
        // 检查新完成的扫描 - 使用之前存储的状态进行比较
        const allLibs = [...(apiData.my_libs?.libs || []), ...(apiData.share2me?.libs || [])];
        allLibs.forEach((lib: any) => {
            const libIdStr = lib.lib_id?.toString();
            const previousStatus = previousLibraryStatusRef.current.get(libIdStr);

            // 如果之前是扫描中，现在是扫描完成，则触发完成状态
            if (previousStatus === '扫描中' && lib.scan_status === '扫描完成' && !completedScans.has(libIdStr)) {
                handleScanCompleted(libIdStr);
            }

            // 更新状态记录
            previousLibraryStatusRef.current.set(libIdStr, lib.scan_status);
        });

        const transformedData: LibraryData = {
            my_libs: {
                count: apiData.my_libs?.count || 0,
                libs: (apiData.my_libs?.libs || []).map((lib: any) => ({
                    id: lib.lib_id.toString(),
                    title: lib.name,
                    cover: lib.poster && lib.poster.length > 0 ? lib.poster : [], // 使用真实的海报数据
                    lastUpdateTime: (() => {
                        const date = new Date(Number(lib.update_time || lib.create_time) * 1000);
                        return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                    })(),
                    paths: lib.scan_path || [],
                    scan_percent: lib.scan_percent,
                    scan_status: lib.scan_status,
                    scan_error_code: lib.scan_error_code,
                    lib_id: lib.lib_id, // 添加lib_id用于扫描
                    tv_visable: lib.tv_visable,
                    share2who_list: lib.share2who_list || []
                }))
            },
            share2me: {
                count: apiData.share2me?.count || 0,
                libs: (apiData.share2me?.libs || []).map((lib: any) => ({
                    id: lib.lib_id.toString(),
                    title: lib.name,
                    cover: lib.poster && lib.poster.length > 0 ? lib.poster : [], // 使用真实的海报数据
                    lastUpdateTime: (() => {
                        const date = new Date(Number(lib.update_time || lib.create_time) * 1000);
                        return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                    })(),
                    sharedBy: lib.share_from ? `用户${lib.share_from}` : '未知用户', // 根据实际数据获取分享者信息
                    scan_percent: lib.scan_percent,
                    scan_status: lib.scan_status,
                    scan_error_code: lib.scan_error_code,
                    lib_id: lib.lib_id, // 添加lib_id用于扫描
                    tv_visable: lib.tv_visable,
                    share2who_list: lib.share2who_list || []
                }))
            }
        };

        setLibraryData(transformedData);

        // 同时更新上下文中的媒体库列表
        const contextLibs = [...(apiData.my_libs?.libs || []), ...(apiData.share2me?.libs || [])].map((lib: any) => ({
            lib_id: lib.lib_id,
            name: lib.name,
            count: lib.media_count,
            status: lib.scan_status
        }));
        setLibs(contextLibs);
    }, [completedScans, handleScanCompleted, setLibs]);

    // 获取媒体库数据
    const fetchLibraryData = useCallback(async (showLoading = true) => {
        try {
            if (showLoading) {
                setLoading(true);
            }

            const response = await request.post('/mediacenter/lib_list', {
                filter: {}
            }, {
                showLoading: showLoading // 传递showLoading参数到request.js
            });

            if (response.code === 0 && response.data) {
                // 由于API实际返回的字段名是 my_libs，需要类型断言
                const apiData = response.data as any;
                updateLibraryDataFromAPI(apiData);

                // 返回allLibraries用于外部判断是否需要轮询
                const allLibraries = [...(apiData.my_libs?.libs || []), ...(apiData.share2me?.libs || [])];
                return allLibraries;
            } else {
                // API返回数据格式不正确时清空列表
                console.warn('API返回数据格式不正确');
                setLibraryData({
                    my_libs: { count: 0, libs: [] },
                    share2me: { count: 0, libs: [] }
                });

                if (showLoading) {
                    Toast.show('API返回数据格式不正确', { duration: 2000 });
                }
                return [];
            }
        } catch (error) {
            console.error('获取媒体库数据失败：', error);
            // 接口失败时清空列表
            setLibraryData({
                my_libs: { count: 0, libs: [] },
                share2me: { count: 0, libs: [] }
            });

            if (showLoading) {
                Toast.show('获取媒体库数据失败，请重试', { duration: 2000 });
            }
            return [];
        } finally {
            if (showLoading) {
                setLoading(false);
            }
        }
    }, [updateLibraryDataFromAPI]); // 减少依赖项

    // 开始轮询扫描状态
    const startPolling = useCallback(() => {
        if (pollingTimerRef.current) {
            clearInterval(pollingTimerRef.current);
        }

        setIsPolling(true); // 标记开始轮询
        // 每3秒轮询一次，不显示loading
        pollingTimerRef.current = setInterval(async () => {
            const allLibraries = await fetchLibraryData(false); // 轮询时不显示loading
            // 检查是否还有正在扫描的媒体库
            if (!hasScanning(allLibraries)) {
                // 如果没有正在扫描的媒体库，则停止轮询
                stopPolling();
            }
        }, 3000);
    }, [fetchLibraryData, hasScanning, stopPolling]);

    // 扫描媒体库
    const handleScanLibrary = useCallback(async (id: string) => {
        console.log('扫描媒体库', id);

        try {
            const lib_id = parseInt(id);
            if (isNaN(lib_id)) {
                Toast.show('媒体库ID无效', { duration: 3000 });
                return;
            }

            setLoading(true);
            const response = await request.post('/mediacenter/lib_scan', { lib_id }, {
                showLoading: true // 用户操作显示loading
            });

            if (response.code === 0) {
                Toast.show('扫描任务已创建', { duration: 2000 });
                // 开始轮询扫描状态
                if (!pollingTimerRef.current) {
                    startPolling();
                }
            } else {
                Toast.show(response.result || '创建扫描任务失败', { duration: 2000 });
            }
        } catch (error) {
            console.error('创建扫描任务失败：', error);
            Toast.show('网络异常，创建扫描任务失败', { duration: 2000 });
        } finally {
            setLoading(false);
        }
    }, [startPolling]);

    // 退出媒体库分享
    const handleExitLibraryShare = useCallback(async (id: string) => {
        const lib_id = parseInt(id);
        if (isNaN(lib_id)) {
            console.error('退出媒体库分享失败：缺少lib_id');
            Toast.show('参数错误，无法退出媒体库', { duration: 2000 });
            return;
        }

        try {
            setLoading(true);
            const response = await request.post('/mediacenter/lib_exit_share', { lib_id }, {
                showLoading: true // 用户操作显示loading
            });

            if (response.code === 0) {
                // 退出成功，从列表中移除该媒体库
                setLibraryData(prev => ({
                    ...prev,
                    share2me: {
                        ...prev.share2me,
                        libs: prev.share2me.libs.filter(item => item.id !== id),
                        count: prev.share2me.libs.filter(item => item.id !== id).length
                    }
                }));

                Toast.show('退出成功', { duration: 2000 });

                // 同时更新上下文中的媒体库列表
                setLibs(prev => prev.filter(lib => lib.lib_id !== lib_id));

                // 刷新媒体库列表
                fetchLibraryData(false); // 不显示loading
            } else {
                Toast.show(response.result || '退出失败，请重试', { duration: 2000 });
            }
        } catch (error) {
            console.error('退出媒体库分享失败：', error);
            Toast.show('网络异常，退出失败', { duration: 2000 });
        } finally {
            setLoading(false);
        }
    }, [fetchLibraryData, setLibs]);

    // 检查是否需要开始轮询的useEffect（初始化时使用）
    const checkAndStartPolling = useCallback(async () => {
        const allLibraries = [...libraryData.my_libs.libs, ...libraryData.share2me.libs];
        if (hasScanning(allLibraries) && !pollingTimerRef.current) {
            startPolling();
        } else if (!hasScanning(allLibraries) && pollingTimerRef.current) {
            stopPolling();
        }
    }, [libraryData.my_libs.libs, libraryData.share2me.libs, hasScanning, startPolling, stopPolling]);

    // 组件卸载时清理定时器
    useEffect(() => {
        return () => {
            stopPolling();
            // 清理所有完成扫描的定时器
            completedScanTimersRef.current.forEach(timer => clearTimeout(timer));
            // eslint-disable-next-line react-hooks/exhaustive-deps
            completedScanTimersRef.current.clear();
        };
    }, [stopPolling]);

    // 初始化媒体库信息
    useEffect(() => {
        // 初始加载显示loading
        fetchLibraryData(true);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // 移除fetchLibraryData依赖，避免无限循环

    // 监听数据变化来决定是否需要轮询（使用防抖来避免频繁调用）
    useEffect(() => {
        const timer = setTimeout(() => {
            checkAndStartPolling();
        }, 100); // 100ms防抖

        return () => clearTimeout(timer);
    }, [checkAndStartPolling]);



    // 处理添加媒体库
    const handleAddLibrary = useCallback(() => {
        console.log('添加媒体库');

        // 检查是否已达到创建上限
        if (libraryData.my_libs.count >= 5) {
            Modal.info({
                title: '',
                icon: null, // 移除左上角图标
                content: (
                    <div>
                        <p style={{ textAlign: 'center', fontSize: '16px', color: 'var(--text-color)', margin: '0 0 20px 0', fontFamily: 'MiSans', fontWeight: '500', padding: '0 25px' }}>
                            你创建的媒体库已满5个，无法继续，请删除后重试
                        </p>
                    </div>
                ),
                okText: '知道了',
                okButtonProps: {
                    style: {
                        backgroundColor: 'var(--user-selector-checkbox-bg)',
                        color: 'var(--button-text-color)',
                        border: 'none',
                        borderRadius: '32px',
                        width: '320px',
                        height: '50px',
                        fontFamily: 'MiSans',
                        fontWeight: '500',
                        fontSize: '16px'
                    }
                },
                centered: true,
                width: 368,
                height: 170,
                maskClosable: false,
                closable: false,
                className: styles.limitModal
            });
            return;
        }

        // 如果未达到上限，正常打开创建弹窗
        setIsCreateModalOpen(true);
    }, [libraryData]);

    // 处理编辑媒体库
    const handleEditLibrary = useCallback((id: string) => {
        console.log('编辑媒体库', id);

        // 在合并的数据中查找要编辑的媒体库
        const library = libraryData.my_libs.libs.find(lib => lib.id === id);

        if (!library) {
            Toast.show('媒体库不存在', { duration: 2000 });
            return;
        }

        // 设置当前编辑的媒体库
        setEditingLibrary(library);
        setIsEditModalOpen(true);
    }, [libraryData]);

    // 处理删除媒体库
    const handleDeleteLibrary = useCallback((id: string) => {
        console.log('删除媒体库', id);
        // 在合并的数据中查找要删除的媒体库
        let library = null;
        let libraryType: 'my_libs' | 'share2me' = 'my_libs';

        // 先在我的媒体库中查找
        library = libraryData.my_libs.libs.find(lib => lib.id === id);
        if (!library) {
            // 如果没找到，在分享给我的媒体库中查找
            library = libraryData.share2me.libs.find(lib => lib.id === id);
            libraryType = 'share2me';
        }

        const libraryName = library?.title || '媒体库';

        // 区分删除和退出分享
        if (libraryType === 'share2me') {
            // 分享给我的媒体库，直接调用退出分享功能
            handleExitLibraryShare(id);
        } else {
            // 我的媒体库，显示删除确认弹窗
            setDeleteLibraryId(id);
            setDeleteLibraryName(libraryName);
            setDeleteLibraryType(libraryType);
            setIsDeleteModalOpen(true);
        }
    }, [libraryData, handleExitLibraryShare]);

    // 处理删除错误
    const handleDeleteError = useCallback((code: number, message: string) => {
        let errorMessage = '删除失败';

        switch (code) {
            case 2203:
                errorMessage = '没有删除该媒体库的权限';
                break;
            case 2205:
                errorMessage = '该媒体库不存在';
                break;
            default:
                errorMessage = message || '删除失败，请重试';
                break;
        }

        Toast.show(errorMessage, { duration: 2000 });
    }, []);

    // 确认删除媒体库
    const handleConfirmDelete = useCallback(async () => {
        setIsDeleteModalOpen(false);

        const lib_id = parseInt(deleteLibraryId);
        if (isNaN(lib_id)) {
            Toast.show('媒体库ID无效', { duration: 2000 });
            return;
        }

        try {
            setLoading(true);
            const response = await request.post('/mediacenter/lib_delete', { lib_id }, {
                showLoading: true // 显示loading
            });

            if (response.code === 0) {
                // 删除成功，从相应的列表中移除
                setLibraryData(prev => {
                    const newData = { ...prev };
                    if (deleteLibraryType === 'my_libs') {
                        newData.my_libs.libs = prev.my_libs.libs.filter(lib => lib.id !== deleteLibraryId);
                        newData.my_libs.count = newData.my_libs.libs.length;
                    } else {
                        newData.share2me.libs = prev.share2me.libs.filter(lib => lib.id !== deleteLibraryId);
                        newData.share2me.count = newData.share2me.libs.length;
                    }
                    return newData;
                });

                Toast.show('删除成功', { duration: 2000 });

                // 同时更新上下文中的媒体库列表
                setLibs(prev => prev.filter(lib => lib.lib_id !== lib_id));

                // 刷新媒体库列表
                fetchLibraryData(false); // 不显示loading
            } else {
                handleDeleteError(response.code, response.result);
            }
        } catch (error) {
            console.error('删除媒体库失败：', error);
            Toast.show('网络异常，删除失败', { duration: 2000 });
        } finally {
            setLoading(false);
        }

        // 重置状态
        setDeleteLibraryId('');
        setDeleteLibraryName('');
        setDeleteLibraryType('my_libs');
    }, [deleteLibraryId, deleteLibraryType, handleDeleteError, fetchLibraryData, setLibs]);

    // 取消删除
    const handleCancelDelete = useCallback(() => {
        setIsDeleteModalOpen(false);
        setDeleteLibraryId('');
        setDeleteLibraryName('');
        setDeleteLibraryType('my_libs');
    }, []);

    // 处理创建弹窗关闭
    const handleCreateModalClose = useCallback(() => {
        setIsCreateModalOpen(false);
    }, []);

    // 处理编辑弹窗关闭
    const handleEditModalClose = useCallback(() => {
        setIsEditModalOpen(false);
        setEditingLibrary(null);
    }, []);



    // 处理创建媒体库成功
    const handleCreateSuccess = useCallback(() => {
        fetchLibraryData(false); // 刷新媒体库列表
    }, [fetchLibraryData]);

    // 处理编辑媒体库成功
    const handleEditSuccess = useCallback(() => {
        fetchLibraryData(false); // 刷新媒体库列表
    }, [fetchLibraryData]);



    // 处理刷新按钮点击
    const handleRefresh = useCallback(() => {
        fetchLibraryData(true); // 显示loading状态刷新数据
    }, [fetchLibraryData]);

    return (
        <div className={styles.container}>
            {/* 页面标题和添加按钮 */}
            <div className={styles.header}>
                <div className={styles.addButton} onClick={handleAddLibrary}>
                    <PreloadImage style={{ width: '20px', height: '20px' }} className={styles.addIcon} src={isDarkMode ? addLib_dark : addLib_light} alt="add" />
                    <span className={styles.addText}>添加媒体库</span>
                </div>
                <div className={styles.refreshBtn} onClick={handleRefresh}>
                    <PreloadImage src={isDarkMode ? refreshBtn_dark : refreshIcon} alt="refresh" />
                </div>
            </div>

            {/* 我创建的媒体库 */}
            <div className={styles.section}>
                <MediaLibraryTable
                    libraries={libraryData.my_libs.libs}
                    onEdit={handleEditLibrary}
                    onDelete={handleDeleteLibrary}
                    onShare={handleScanLibrary}
                    sectionTitle={`我创建的（${libraryData.my_libs.count}个）`}
                    scanProgress={scanProgress}
                    completedScans={completedScans}
                />
            </div>

            {/* 他人分享的媒体库 */}
            <div className={styles.section}>
                <MediaLibraryTable
                    libraries={libraryData.share2me.libs}
                    isShared={true}
                    onEdit={handleEditLibrary}
                    onDelete={handleDeleteLibrary}
                    onShare={handleScanLibrary}
                    onExitShare={handleExitLibraryShare}
                    sectionTitle={`他人分享（${libraryData.share2me.count}个）`}
                    scanProgress={scanProgress}
                    completedScans={completedScans}
                />
            </div>

            {/* 创建媒体库弹窗 */}
            <CreateLibraryModal
                visible={isCreateModalOpen}
                onClose={handleCreateModalClose}
                onSuccess={handleCreateSuccess}
                libraryCount={libraryData.my_libs.count}
            />

            {/* 编辑媒体库弹窗 */}
            {editingLibrary && (
                <CreateLibraryModal
                    visible={isEditModalOpen}
                    onClose={handleEditModalClose}
                    onSuccess={handleEditSuccess}
                    libraryCount={libraryData.my_libs.count}
                    isEditMode={true}
                    editData={{
                        lib_id: editingLibrary.lib_id || 0,
                        name: editingLibrary.title,
                        tv_visable: (editingLibrary as any).tv_visable || 0,
                        scan_path: editingLibrary.paths || [],
                        share2who_list: (editingLibrary as any).share2who_list || []
                    }}
                />
            )}

            {/* 删除确认弹窗 */}
            <Modal
                title={`确认删除"${deleteLibraryName}"?`}
                className={styles.deleteModal}
                open={isDeleteModalOpen}
                onCancel={handleCancelDelete}
                footer={null}
                width={400}
                centered
                destroyOnClose={true}
                maskClosable={false}
            >
                <div className={styles.deleteModalContent}>
                    <p className={styles.deleteWarning}>
                        此操作仅将媒体库从影视服务器中移除，媒体文件将不会受到影响。
                    </p>
                    <div className={styles.deleteModalButtons}>
                        <Button
                            className={styles.cancelBtn}
                            onClick={handleCancelDelete}
                        >
                            取消
                        </Button>
                        <Button
                            className={styles.confirmBtn}
                            onClick={handleConfirmDelete}
                        >
                            确定
                        </Button>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default LibraryManagement;