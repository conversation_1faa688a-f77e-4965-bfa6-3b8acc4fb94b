import { FC } from "react";
import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";
import content_error from '@/Resources/filmWall/content_error_dark.png';
import content_null from '@/Resources/filmWall/content_null_dark.png';
import lib_null from '@/Resources/filmWall/lib_null.png';
import TVFocusable from "../TVFocus";
import XMLoading from "@/components/XMLoading";

interface ErrorProps {
  text: string;
  subText?: string;
  isError: boolean;
  hasLibrary: boolean;
  hasContent: boolean;
  refresh: () => void;
  customizeRow?: number
  customizeCol?: number;
  loading?: boolean;
}

const ErrorComponentTV: FC<ErrorProps> = ({
  text, subText, isError, hasContent = true, hasLibrary = true, refresh, children, customizeRow, customizeCol, loading = true
}) => {
  return <>
    {
      (!isError && hasContent && hasLibrary) ?
        children
        : (
          <div className={styles.error_container}>
            {loading ? <XMLoading loading={true} size={60} theme="dark" /> :
              <div className={styles.error_content}>
                <div className={styles.error_img}>
                  <PreloadImage src={isError ? content_error : !hasLibrary ? lib_null : !hasContent ? content_null : ''} alt="error_icon" />
                </div>
                <div className={styles.error_text}>
                  <span>{text}</span>
                </div>
                {
                  !isError ? (
                    <div className={styles.error_subText}>{subText}</div>
                  ) :
                    (
                      <TVFocusable id={`tv-id-error-retry`} row={customizeRow || 1} col={customizeCol || 1} className={styles.error_retryBtn} onClick={() => refresh()}>重试</TVFocusable>
                    )
                }
              </div>
            }
          </div>
        )
    }
  </>
}

export default ErrorComponentTV;