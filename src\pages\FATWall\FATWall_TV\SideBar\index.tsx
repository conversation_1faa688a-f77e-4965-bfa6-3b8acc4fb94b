import React, { FC, useCallback, useEffect, useMemo, useState } from "react";
import styles from './index.module.scss';
import TVFocusable from "../TVFocus";
import { PreloadImage } from "@/components/Image";
import { useHistory, useRouteMatch } from "react-router-dom";
import { FAT_TV_PREFIX_PATH, useLibraryListTV } from "..";
import search_icon_tv from '@/Resources/filmWall/search_icon_tv.png';
import search_icon_tv_dark from '@/Resources/filmWall/search_icon_tv_dark.png';
import { videoWallUserOptions } from "@/api/fatWallJSBridge";
import user_avatar from '@/Resources/filmWall/user_avatar.png';
import { AccountInfo, getAccountInfo } from "@/utils/DeviceType";

interface IOptions {
  name: string;
  label?: string;
  path: string;
}

interface IOptOptions extends IOptions {
  icon: string;
}

export const TV_SIDE_BAR_KEY = 'tvSideBarKey';


const FilmSideBar: FC = () => {
  const history = useHistory();
  const routeMatch = useRouteMatch();
  const [searchIcon, setSearchIcon] = useState<string>(search_icon_tv);

  const [userInfo, setUserInfo] = useState<AccountInfo>();

  const currentPath = useMemo(() => {
    const path = routeMatch.path.split('/');
    if (path.length > 2) {
      return path[2];
    }

    return 'recentlyPlay';
  }, [routeMatch.path])

  //功能页的icon
  const pageIconList: Omit<IOptions, 'src'>[] = useMemo(() => {
    return [
      {
        name: "recentlyPlay",
        label: '最近观看',
        path: `${FAT_TV_PREFIX_PATH}`
      },
      {
        name: "recentlyAdd",
        label: '最近添加',
        path: `${FAT_TV_PREFIX_PATH}/recentlyAdd`
      },
      {
        name: "all",
        label: '全部',
        path: `${FAT_TV_PREFIX_PATH}/all`
      },
      {
        name: "collect",
        label: '收藏',
        path: `${FAT_TV_PREFIX_PATH}/collect`
      },
      {
        name: "played",
        label: '已观看',
        path: `${FAT_TV_PREFIX_PATH}/played`
      },
    ];
  }, []);

  //工具类icon
  const optIconList: IOptOptions[] = useMemo(() => [
    {
      name: "search",
      label: '搜索',
      icon: searchIcon,
      path: `${FAT_TV_PREFIX_PATH}/search`
    },
  ], [searchIcon]);

  const libs = useLibraryListTV();

  //媒体库icon
  const libraryIconList: IOptions[] = useMemo(() => {
    return [...libs.libs.slice().reverse().map((item) => {
      return {
        name: item.name,
        label: item.name,
        path: `${FAT_TV_PREFIX_PATH}/library/${item.lib_id}`
      }
    })];
  }, [libs.libs]);

  // 初始化的时候从app端获取用户信息
  useEffect(() => {
    const info = getAccountInfo();
    setUserInfo(info);
  }, [])

  const userInfoOnClick = useCallback(async () => {
    await videoWallUserOptions()
  }, [])

  return (
    <div className={styles["sidebar_container"]}>
      {
        userInfo && <>
          <TVFocusable id={'tv-id-sideBarUser-avatar'} row={0} col={0} className={styles.user_avatar}
            onClick={userInfoOnClick}>
            <PreloadImage key={userInfo.uid} src={userInfo.icon || user_avatar} alt={"user"} />
          </TVFocusable>
          <div className={styles["sidebar_line"]}>
            <PreloadImage src={require("@/Resources/filmWall/line.png")} alt={"line"} />
          </div>
        </>
      }

      {
        /* 工具栏icon */
        optIconList.map((item: IOptOptions, index: number) => (
          <TVFocusable id={`tv-id-sideBarSearch-${item.name}-${index}`} currentItemCallback={(item) => localStorage.setItem(TV_SIDE_BAR_KEY, item.id)} focusEvent={() => setSearchIcon(search_icon_tv_dark)} blurEvent={() => setSearchIcon(search_icon_tv)}
            row={index + 1} col={0} key={`optIcon` + index} className={`${styles["sidebar_opt"]} ${currentPath === item.name ? styles.selected : ''}`} onClick={() => history.push(item.path)}>
            <PreloadImage key={item.name} src={item.icon} alt={item.name} />
            <span>{item.label}</span>
          </TVFocusable>
        ))
      }

      {/* 下划线 */}
      <div className={styles["sidebar_line"]}>
        <PreloadImage src={require("@/Resources/filmWall/line.png")} alt={"line"} />
      </div>

      <div className={styles.sidebar_item_container}>
        {
          /* 页面标题icon */
          pageIconList.map((item: IOptions, index: number) => (
            <TVFocusable id={`tv-id-sideBar-${item.name}-${index}`} currentItemCallback={(item) => localStorage.setItem(TV_SIDE_BAR_KEY, item.id)}
              row={optIconList.length + index + 1} col={0} key={`pageIcon` + index} className={`${styles["sidebar_opt"]} ${currentPath === item.name ? styles.selected : ''}`} onClick={() => history.push(item.path)}>
              <span>{item.label}</span>
            </TVFocusable>
          ))
        }
      </div>

      {/* 下划线 */}

      {
        libraryIconList.length > 0 && <div className={styles["sidebar_line"]}>
          <PreloadImage src={require("@/Resources/filmWall/line.png")} alt={"line"} />
        </div>
      }
      <div className={styles.sidebar_item_container}>
        {
          /* 媒体库icon */
          libraryIconList.map((item: IOptions, index: number) => (
            <TVFocusable id={`tv-id-sideBar-${item.name}-${index}`} currentItemCallback={(item) => localStorage.setItem(TV_SIDE_BAR_KEY, item.id)}
              row={optIconList.length + pageIconList.length + index + 1} col={0} key={`pageIcon_${index}_${item.name}`} className={`${styles["sidebar_opt"]} ${currentPath === item.name ? styles.selected : ''}`}
              onClick={() => {
                // 存储媒体库状态到localStorage - TV端
                const libData = libs.libs.find(lib => lib.name === item.name);
                if (libData) {
                  const libraryState = {
                    title: libData.name,
                    lib_id: libData.lib_id
                  };
                  localStorage.setItem('libraryState', JSON.stringify(libraryState));
                }
                
                history.push({ pathname: item.path, state: { data: item } });
              }}>
              <span>{item.label}</span>
            </TVFocusable>
          ))
        }
      </div>
    </div>
  );
}

export default FilmSideBar;
