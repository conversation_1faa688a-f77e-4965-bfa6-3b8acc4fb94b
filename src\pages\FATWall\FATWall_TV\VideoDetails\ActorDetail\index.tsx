import React, { useEffect, useState, useCallback } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import FilmCard from '@/components/FATWall_PC/FilmCard';
import { searchLocalMedia } from '@/api/fatWall';
import styles from './index.module.scss';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { PreloadImage } from '@/components/Image';

interface ActorDetailProps {
    profile_path?: string;
    name?: string;
}

interface SearchResult {
    id: string;
    title: string;
    year: string;
    country: string;
    genres: string;
    posterUrl: string;
    type: string;
    media_id: number;
    classes: string;
    score: number;
}

const ActorDetail: React.FC = () => {
    const history = useHistory();
    const location = useLocation();

    // 从URL参数或state中获取演员信息
    const urlParams = new URLSearchParams(location.search);
    const urlProfilePath = urlParams.get('profile_path');
    const urlName = urlParams.get('name');

    const stateParams = location.state as ActorDetailProps || {};

    const profile_path = urlProfilePath || stateParams.profile_path || '';
    const name = urlName || stateParams.name || '未知演员';

    const [relatedWorks, setRelatedWorks] = useState<SearchResult[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    // 处理焦点滚动
    const handleFocusScroll = useCallback((item: any) => {
        // TV版本的焦点滚动处理逻辑
        if (item && item.ref && item.ref.current) {
            item.ref.current.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }, []);

    // 搜索相关作品
    const { run: runSearchRelatedWorks } = useRequest(
        async (actorName: string) => {
            if (!actorName.trim()) return [];

            const response = await searchLocalMedia({
                keyword: actorName.trim(),
                filter: {
                    offset: 0,
                    limit: 50,
                    classes: "" // 搜索所有类型
                }
            });

            if (response.code === 0 && response.data) {
                const { medias } = response.data;

                // 转换数据格式
                return medias.map(media => ({
                    id: media.media_id.toString(),
                    title: media.trans_name || media.origin_name || '未知影片',
                    year: media.year?.toString() || '',
                    country: media.origin_place || '',
                    genres: media.kind?.join(', ') || '',
                    posterUrl: media.poster && media.poster.length > 0 ? media.poster[0] : '',
                    type: media.classes || '',
                    media_id: media.media_id,
                    classes: media.classes || '',
                    score: media.score || 0
                }));
            }

            return [];
        },
        {
            manual: true,
            onSuccess: (data) => {
                setRelatedWorks(data);
                setLoading(false);
            },
            onError: () => {
                message.error('获取相关作品失败');
                setLoading(false);
            },
        }
    );

    // 组件加载时搜索相关作品
    useEffect(() => {
        if (name && name !== '未知演员') {
            setLoading(true);
            runSearchRelatedWorks(name);
        }
    }, [name, runSearchRelatedWorks]);

    // 处理作品卡片点击
    const handleWorkClick = useCallback((work: SearchResult) => {
        // 跳转到影片详情页
        const params = new URLSearchParams({
            classes: work.classes || '',
            media_id: work.media_id.toString(),
            lib_id: '0'
        });

        history.push(`/filmAndTelevisionWall_tv/videoDetails?${params.toString()}`);
    }, [history]);

    return (
        <div className={styles.container}>
            {/* 顶部演员信息 */}
            <div className={styles.headerSection}>
                <div className={styles.actorAvatar}>
                    {profile_path ? (
                        <PreloadImage src={profile_path} alt="" style={{ height: '150px', width: '150px', borderRadius: '50%' }} />
                    ) : (
                        name.charAt(0)
                    )}
                </div>
                <div className={styles.leftSection}>
                    <div className={styles.actorName}>{name}</div>
                </div>
            </div>

            {/* 相关作品 */}
            <div className={styles.worksSection}>
                <h2 className={styles.sectionTitle}>相关作品</h2>
                {loading ? (
                    <div className={styles.loadingContainer}>
                        <div className={styles.loadingSpinner}></div>
                    </div>
                ) : relatedWorks.length > 0 ? (
                    <div className={styles.worksGrid}>
                        {relatedWorks.map((work, index) => (
                            <FilmCard
                                key={work.id}
                                id={`tv-focus-actorDetail-work-${work.id}`}
                                row={0}
                                col={index}
                                url={work.posterUrl}
                                score={work.score.toString()}
                                name={work.title}
                                title={work.title}
                                favourite={false}
                                media_id={work.media_id}
                                classes={work.classes}
                                currentItemCallback={handleFocusScroll}
                                onClick={() => handleWorkClick(work)}
                            />
                        ))}
                    </div>
                ) : (
                    <div className={styles.emptyContainer}>
                        <div className={styles.emptyText}>暂无相关作品</div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ActorDetail;