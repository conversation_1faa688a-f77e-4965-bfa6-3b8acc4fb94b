import { Sniffer } from "xgplayer";
import styles from "./index.module.scss";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Player } from "xgplayer/es/instManager";
import { Route, Switch, useLocation, useRouteMatch, useHistory } from "react-router-dom";
import NavigatorBar from "@/components/NavBar";
import backIcon_light from "@/Resources/icon/backIcon_light.png";
import backIcon_dark from "@/Resources/icon/backIcon_dark.png";
import { useTheme } from "@/utils/themeDetector";
import LookBackDetail from "./LookBackDetail";
import EventLookBack from "./EventLookBack";
import MonitorPlayer from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
import TimeAxis, { IEventBlock } from "@/components/CameraPlayer/components/TimeAxis/TimeAxis";
import EventOperate from "@/components/CameraPlayer/components/EventOperate/EventOperate";
import { eventDefinition } from "@/components/CameraPlayer/constants";
import { CameraEventDataVideoProps, startLiveWithCamera, stopLiveWithCamera } from "@/api/ipc";
import { useRequest } from "ahooks";
import { needOpenCamera } from "@/api/cameraPlayer";
import { Toast } from "@/components/Toast/manager";

export interface IKey_frame {
  lens_id: number,
  frame: string,
  psm: boolean
}
export interface ICameraDetail {
  did: string,
  name: string,
  model: string,
  record_enabled: boolean,
  ip: string,
  mac: string,
  record_period: number,
  used_space: number,
  space_limit: number,
  key_frame: IKey_frame[],
  isOnline: boolean
}

export interface ILiveCameraData {
  [key: string]: {
    key_frame: IKey_frame[];
    hls_file: string
  }
}

export interface IEventVideo extends CameraEventDataVideoProps {
  id: string,
}

const CameraDetail = () => {
  const cameraRef1 = useRef<Player | null>(null);
  const cameraRef2 = useRef<Player | null>(null);
  const cameraRef3 = useRef<Player | null>(null);
  const [eventData, setEventData] = useState<IEventVideo[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  const [liveCameraData, setLiveCameraData] = useState<ILiveCameraData>();
  const location = useLocation<{ cameraDetail: ICameraDetail }>(); // 获取当前摄像机信息
  const [cameraDetail, setCameraDetail] = useState<ICameraDetail | null>(location.state?.cameraDetail || null); // 获取当前摄像机信息


  const [curTime, setCurTime] = useState<Date>(new Date()); // 获得时间轴当前时间
  const [isPause, setIsPause] = useState<boolean>(false);

  const { runAsync } = useRequest(startLiveWithCamera, {
    debounceWait: 300,
    manual: true
  })

  // 获取当前时间轴事件id
  const [currentEventKey, setCurrentEventKey] = useState<string>('');

  // 获取摄像机详情信息
  const getCameraDetail = useCallback(async () => {
    await needOpenCamera((res) => {
      console.log('hik获取摄像机详情信息:', JSON.stringify(res));
      if (res && res.code === 0 && res.data && res.data.camera) {
        setCameraDetail(res.data.camera);
      }
    }).catch((e) => {
      console.log('hik获取摄像机详情失败', e);
    });
  }, [])

  // 初始化获得
  useEffect(() => {
    // 如果是从摄像机管理页面通过state传递过来的，正常获取
    if (location.state && location.state.cameraDetail) {
      console.log('管理页进入,使用路由state');
      setCameraDetail(location.state.cameraDetail);
    } else {
      console.log('首页小组件进入,通过hik传递did');
      getCameraDetail();
    }
  }, [getCameraDetail, location.state])

  // 摄像头数量
  const camera_lens = useMemo(() => {
    if (!cameraDetail) return [];
    return cameraDetail.key_frame.map((item) => (`${cameraDetail.did}_${item.lens_id}`))
  }, [cameraDetail])

  // 开启直播
  const startLive = useCallback(async (time?: string) => {
    if (camera_lens.length === 0) {
      return;
    }

    const res = await runAsync(camera_lens, time).catch((e) => console.log('开启直播失败,', e));
    if (res && res.code === 0 && res.data) {
      setLiveCameraData(res.data);
    }

    if (res && res.code !== 0) {
      Toast.show(res.result);
    }
  }, [camera_lens, runAsync])

  const closeLive = useCallback(async () => {
    if (!cameraDetail) return;
    const result: string[] = [];
    cameraDetail.key_frame.forEach((item) => {
      result.push(`${cameraDetail.did}_${item.lens_id}`);
    })

    const res = await stopLiveWithCamera(result);
    if (res && res.code === 0) {
      console.log('关闭直播成功,摄像头:', result);
    }

    if (res && res.code !== 0) {
      console.log(res.result);
    }
  }, [cameraDetail])

  useEffect(() => {
    startLive();

    return () => {
      // 组件卸载时关闭直播并销毁播放器实例
      closeLive();
      if (cameraRef1 && cameraRef1.current) cameraRef1.current.destroy();
      if (cameraRef2 && cameraRef2.current) cameraRef2.current.destroy();
      if (cameraRef3 && cameraRef3.current) cameraRef3.current.destroy();
    }
  }, [closeLive, startLive]);

  // 时间轴事件
  const timeAxisEvents = useMemo(() => {
    const tempObj: { [key: string]: IEventBlock[] } = {}
    camera_lens.forEach((item) => {
      tempObj[item] = eventData.filter((it) => it.camera_lens !== item).map((it) => {
        const date = new Date(Number(it.time));
        const h = date.getHours();
        const m = date.getMinutes();
        const s = date.getSeconds();
        return {
          ...it,
          event_name: it.event_name,
          day: date.getDate(),
          start: h * 3600 + m * 60 + s, end: h * 3600 + m * 60 + s + it.media_duration,
          color: it.event_name !== '' ? eventDefinition[it.event_name].color : 'rgba(229, 229, 229, 1)',
          file: it.file,
          time: it.time
        }
      })
    })
    return tempObj;
  }, [camera_lens, eventData])

  // 多摄情况下显示的播放器
  const dualPlayer = useMemo(() => {
    if (!liveCameraData || !cameraDetail) return <></>;
    const playerWidth = `${window.innerWidth}px`;

    // 双摄情况下显示两个播放器
    if (cameraDetail.key_frame.length === 2) {
      if (!liveCameraData[`${cameraDetail.did}_0`] && !liveCameraData[`${cameraDetail.did}_1`]) return <></>;
      return <>
        <MonitorPlayer initPlayStatus={false} eventData={eventData} selectedDate={selectedDate} baseConfig={
          { url: liveCameraData[`${cameraDetail.did}_0`]?.hls_file, type: 'Live', mediaName: `${cameraDetail.did}_0`, width: playerWidth }
        } cameraRef={cameraRef1}
          dualOptions={{
            urls: {
              main: liveCameraData[`${cameraDetail.did}_0`]?.hls_file,
              secondary: liveCameraData[`${cameraDetail.did}_1`]?.hls_file
            },
            poster: {
              main: cameraDetail.key_frame[0].frame,
              secondary: cameraDetail.key_frame[1].frame
            },
            psm: {
              main: cameraDetail.key_frame[0].psm,
              secondary: cameraDetail.key_frame[1].psm
            },
            cameraRef_secondary: cameraRef2
          }} setSelectedDate={setSelectedDate} cameraDetail={cameraDetail} isOnline={cameraDetail.isOnline} poster={cameraDetail.key_frame[0].frame} />

        <MonitorPlayer initPlayStatus={false} eventData={eventData} selectedDate={selectedDate} baseConfig={
          { url: liveCameraData[`${cameraDetail.did}_1`]?.hls_file, type: 'Live', mediaName: `${cameraDetail.did}_1`, width: playerWidth }
        } cameraRef={cameraRef2}
          dualOptions={{
            urls: {
              main: liveCameraData[`${cameraDetail.did}_1`]?.hls_file,
              secondary: liveCameraData[`${cameraDetail.did}_0`]?.hls_file
            },
            poster: {
              main: cameraDetail.key_frame[1].frame,
              secondary: cameraDetail.key_frame[0].frame
            },
            psm: {
              main: cameraDetail.key_frame[1].psm,
              secondary: cameraDetail.key_frame[0].psm
            },
            cameraRef_secondary: cameraRef1
          }} setSelectedDate={setSelectedDate} cameraDetail={cameraDetail} isOnline={cameraDetail.isOnline} poster={cameraDetail.key_frame[1].frame} />
      </>
    }

    // 多摄情况下显示三个播放器
    if (cameraDetail.key_frame.length > 2) {
      if (!liveCameraData[`${cameraDetail.did}_0`] && !liveCameraData[`${cameraDetail.did}_1`] && !liveCameraData[`${cameraDetail.did}_2`]) return <></>;
      return <>
        <MonitorPlayer initPlayStatus={false} eventData={eventData} selectedDate={selectedDate} baseConfig={{
          url: liveCameraData[`${cameraDetail.did}_0`]?.hls_file, type: 'Live', mediaName: `${cameraDetail.did}_0`, width: playerWidth
        }} cameraRef={cameraRef1}
          dualOptions={{
            urls: {
              main: liveCameraData[`${cameraDetail.did}_0`]?.hls_file,
              secondary: liveCameraData[`${cameraDetail.did}_1`]?.hls_file,
              third: liveCameraData[`${cameraDetail.did}_2`]?.hls_file
            },
            poster: {
              main: cameraDetail.key_frame[0].frame,
              secondary: cameraDetail.key_frame[1].frame,
              third: cameraDetail.key_frame[2].frame
            },
            psm: {
              main: cameraDetail.key_frame[0].psm,
              secondary: cameraDetail.key_frame[1].psm,
              third: cameraDetail.key_frame[2].psm
            },
            cameraRef_secondary: cameraRef2,
            cameraRef_third: cameraRef3
          }} setSelectedDate={setSelectedDate} cameraDetail={cameraDetail} isOnline={cameraDetail.isOnline} poster={cameraDetail.key_frame[0].frame} />

        <div className={styles.third_player_container}>
          <MonitorPlayer initPlayStatus={false} eventData={eventData} selectedDate={selectedDate}
            baseConfig={{ url: liveCameraData[`${cameraDetail.did}_1`]?.hls_file, type: 'Live', mediaName: `${cameraDetail.did}_1`, width: `${window.innerWidth / 2}px` }} cameraRef={cameraRef2}
            dualOptions={{
              urls: {
                main: liveCameraData[`${cameraDetail.did}_1`]?.hls_file,
                secondary: liveCameraData[`${cameraDetail.did}_0`]?.hls_file,
                third: liveCameraData[`${cameraDetail.did}_2`]?.hls_file
              },
              poster: {
                main: cameraDetail.key_frame[1].frame,
                secondary: cameraDetail.key_frame[0].frame,
                third: cameraDetail.key_frame[2].frame
              },
              psm: {
                main: cameraDetail.key_frame[1].psm,
                secondary: cameraDetail.key_frame[0].psm,
                third: cameraDetail.key_frame[2].psm
              },
              cameraRef_secondary: cameraRef1,
              cameraRef_third: cameraRef3
            }} setSelectedDate={setSelectedDate} cameraDetail={cameraDetail} isOnline={cameraDetail.isOnline} poster={cameraDetail.key_frame[1].frame} />
          <MonitorPlayer initPlayStatus={false} eventData={eventData} selectedDate={selectedDate}
            baseConfig={{ url: liveCameraData[`${cameraDetail.did}_2`]?.hls_file, type: 'Live', mediaName: `${cameraDetail.did}_2`, width: `${window.innerWidth / 2}px` }} cameraRef={cameraRef3}
            dualOptions={{
              urls: {
                main: liveCameraData[`${cameraDetail.did}_2`]?.hls_file,
                secondary: liveCameraData[`${cameraDetail.did}_0`]?.hls_file,
                third: liveCameraData[`${cameraDetail.did}_1`]?.hls_file
              },
              poster: {
                main: cameraDetail.key_frame[2].frame,
                secondary: cameraDetail.key_frame[0].frame,
                third: cameraDetail.key_frame[1].frame
              },
              psm: {
                main: cameraDetail.key_frame[2].psm,
                secondary: cameraDetail.key_frame[0].psm,
                third: cameraDetail.key_frame[1].psm
              },
              cameraRef_secondary: cameraRef1,
              cameraRef_third: cameraRef2
            }} setSelectedDate={setSelectedDate} cameraDetail={cameraDetail} isOnline={cameraDetail.isOnline} poster={cameraDetail.key_frame[2].frame} />
        </div>
      </>
    }

    return <></>
  }, [cameraDetail, eventData, liveCameraData, selectedDate])

  if (!cameraDetail) return <div className={styles.cameraPlayer_container_error}>初始化失败</div>;

  return (
    <div className={styles.cameraPlayer_default_container}>
      {
        liveCameraData && (
          cameraDetail.key_frame.length === 1 ?
            <MonitorPlayer initPlayStatus={false} okCallback={() => startLive(String(curTime.getTime()))} setIsPause={setIsPause} eventData={eventData}
              selectedDate={selectedDate} setSelectedDate={setSelectedDate} baseConfig={{
                url: liveCameraData ? liveCameraData[`${cameraDetail.did}_0`]?.hls_file : '',
                type: 'Live', mediaName: `${cameraDetail.did}_0`,
                width: `${window.innerWidth}px`
              }}
              cameraRef={cameraRef1} isOnline={cameraDetail.isOnline} cameraDetail={cameraDetail} poster={cameraDetail.key_frame[0].frame} />
            : dualPlayer
        )
      }
      <EventOperate currentEventKey={currentEventKey} eventData={eventData} camera_lens={camera_lens} controlEventFilter={setEventData}
        setSelectedDate={setSelectedDate} selectedDate={selectedDate} needEvent={true}>
        {
          liveCameraData && (
            <TimeAxis onChange={setCurrentEventKey} isPause={isPause} playerOpt={
              {
                player: { main: cameraRef1, secondary: cameraRef2, third: cameraRef3 },
                url: {
                  main: liveCameraData[`${cameraDetail.did}_0`]?.hls_file,
                  secondary: cameraDetail.key_frame.length === 1 ? '' : liveCameraData[`${cameraDetail.did}_1`]?.hls_file,
                  third: cameraDetail.key_frame.length > 2 ? liveCameraData[`${cameraDetail.did}_2`]?.hls_file : ''
                }
              }
            } events={timeAxisEvents} type={'Live'} isFull={false} deviceType={Sniffer.device} current={selectedDate}
              setCurrent={setSelectedDate} setCurTime={setCurTime} />
          )
        }
      </EventOperate>
    </div>
  )
}

const IPCCameraDetail = () => {

  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();
  const location = useLocation();
  const history = useHistory();

  // 自定义返回函数
  const handleBack = useCallback(() => {
    // 检查当前路径是否是lookBackDetail
    if (location.pathname.includes('/lookBackDetail')) {
      // 检查是否是从人脸详情页跳转过来的
      const state = location.state as any;
      if (state?.fromPage === 'faceDetail' && state?.face) {
        // 返回到人脸详情页，并带上face数据
        history.replace({
          pathname: `/cameraManagement_app/faceRecognition/faceDetail/${state.face?.uuid}`,
          state: { face: state.face }
        });
        return;
      }
    }

    if (location.pathname.includes('/cameraDetail')) {
      history.push('/cameraManagement_app');
      return;
    }

    // 默认返回逻辑
    history.goBack();
  }, [location, history]);

  return (
    <div className={styles.cameraPlayer_container}>
      <NavigatorBar title="家里" backIcon={isDarkMode ? backIcon_dark : backIcon_light} onBack={handleBack} />
      <div className={styles.cameraPlayer_content}>
        <Switch>
          {/* 子路由出口 */}
          <Route exact path={`${path}/lookBackDetail`}>
            <LookBackDetail />
          </Route>
          <Route exact path={`${path}/eventLookBack`}>
            <EventLookBack />
          </Route>

          {/* 默认内容 */}
          <Route path={path}>
            <CameraDetail />
          </Route>
        </Switch>
      </div>
    </div>
  )
}

export default IPCCameraDetail;