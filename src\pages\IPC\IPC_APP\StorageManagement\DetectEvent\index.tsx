import { useState, useEffect } from "react";
import { Image, List, Switch, Toast } from "antd-mobile";
import { useLocation } from "react-router-dom";
import styles from "./index.module.scss";
import { useStorage } from "../Context/storageContext";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import enterRight from "@/Resources/camMgmtImg/enter-right.png";
import detect from "@/Resources/player/move.png";
import humanoid from "@/Resources/player/human.png";
import fireworks from "@/Resources/player/fire.png";
import pet from "@/Resources/player/pet.png";
// import abnormal from "@/Resources/player/noise.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import enterRightDark from "@/Resources/camMgmtImg/enter-right-dark.png";
import NavigatorBar from "@/components/NavBar";
import PopoverSelector,{ Option } from "@/components/PopoverSelector";
import { useTheme } from "@/utils/themeDetector";
import { getRecordConfig, setupRecordCamera, CameraInfo } from "@/api/ipc";
import { useRequest } from "ahooks";

interface LocationState {
  camera?: CameraInfo;
}

// 敏感度枚举转换
const sensitivityMap = {
  "低灵敏度": 0,
  "中灵敏度": 1,
  "高灵敏度": 2
};

// 敏感度数值转UI文本
const getSensitivityText = (value: number): string => {
  if (value === 0) return "低灵敏度";
  if (value === 1) return "中灵敏度";
  return "高灵敏度";
};

// 检测事件类型映射
const eventTypeMap = {
  "move": "motionDetect",
  "human": "humanDetect",
  "fire": "fireDetect", 
  "pet": "petDetect",
  // "noise": "noiseDetect"
};

const EventDetection = () => {
  const location = useLocation<LocationState>();
  const { isDarkMode } = useTheme();
  const { detectEvents, setDetectEvents } = useStorage();
  
  // 获取传递的摄像机数据
  const [camera, setCamera] = useState<CameraInfo | null>(null);
  
  // 灵敏度相关状态
  const [sensitivityPopoverVisible, setSensitivityPopoverVisible] = useState(false);
  const [sensitivity, setSensitivity] = useState("中灵敏度");

  // 灵敏度选项
  const sensitivityOptions: Option[] = [
    { label: "低灵敏度", value: "低灵敏度" },
    { label: "中灵敏度", value: "中灵敏度" },
    { label: "高灵敏度", value: "高灵敏度" },
  ];

  // 从location获取摄像机信息
  useEffect(() => {
    if (location.state?.camera) {
      setCamera(location.state.camera);
    }
  }, [location.state]);

  // 获取录制配置
  const { run: fetchConfig } = useRequest(getRecordConfig, {
    manual: true,
    onSuccess: (result) => {
      if(result.code !== 0){
        Toast.show(result?.result);
        return;
      }
      const recordConfig = result.data;
      
      // 更新事件开关状态
      if (recordConfig.event_trigger && Array.isArray(recordConfig.event_trigger)) {
        const eventStates = {
          motionDetect: false,
          humanDetect: false,
          fireDetect: false,
          petDetect: false,
          // noiseDetect: false
        };
        
        // 从响应更新检测事件状态
        recordConfig.event_trigger.forEach(event => {
          const eventType = eventTypeMap[event.name as keyof typeof eventTypeMap];
          if (eventType) {
            eventStates[eventType as keyof typeof eventStates] = event.enabled;
          }
          
          // 设置动作检测灵敏度
          if (event.name === "move") {
            setSensitivity(getSensitivityText(event.sensitivity));
          }
        });
        
        setDetectEvents(eventStates);
      }
    },
    onError: (error) => {
      console.error('获取检测事件配置失败:', error);
      Toast.show({
        content: '获取配置失败',
        position: 'bottom',
      });
    }
  });

  // 保存检测事件设置
  const { run: saveConfig } = useRequest(setupRecordCamera, {
    manual: true,
    onSuccess: (result) => {
      if(result && (result.code !== 0)){
        Toast.show(result?.result);
        return;
      }
      Toast.show({
        content: '设置成功',
        position: 'bottom',
      });
    },
    onError: () => {
      Toast.show({
        content: '设置失败，请重试',
        position: 'bottom',
      });
    }
  });

  // 当摄像机ID可用时获取配置
  useEffect(() => {
    if (camera?.did) {
      fetchConfig({ camera: camera.did });
    }
  }, [camera, fetchConfig]);

  // 页面曝光埋点
  useEffect(() => {
    window.onetrack?.('track', 'ipc_recordConfig_detectEvent_expose');
  }, []);

  // 处理灵敏度选择
  const handleSensitivityChange = (value: string) => {
    if (!camera?.did) return;
    
    setSensitivity(value);
    
    // 更新灵敏度
    const apiSensitivity = sensitivityMap[value as keyof typeof sensitivityMap] ?? 1;
    
    //请求参数
    const eventTriggers = Object.entries(eventTypeMap).map(([apiName, uiName]) => {
      return {
        name: apiName,
        enabled: detectEvents[uiName as keyof typeof detectEvents],
        sensitivity: apiName === "move" ? apiSensitivity : 1 // 只更新动作检测的灵敏度
      };
    });
    
    saveConfig({
      camera: [camera.did],
      config: {
        event_trigger: eventTriggers
      }
    });
  };

  // 处理开关变化
  const handleSwitchChange = (key: keyof typeof detectEvents) => (value: boolean) => {
    if (!camera?.did) return;

    // 添加事件触发埋点
    const eventNameMap = {
      motionDetect: 'move',
      humanDetect: 'human',
      fireDetect: 'fire',
      petDetect: 'pet',
      // noiseDetect: 'noise'
    };

    const eventName = eventNameMap[key];
    if (eventName) {
      if (value) {
        window.onetrack?.('track', 'ipc_recordConfig_event_trigger_enable', { [eventName]: value });
      } else {
        window.onetrack?.('track', 'ipc_recordConfig_event_trigger_disable', { [eventName]: value });
      }
    }

    // 更新本地状态
    setDetectEvents((prev) => ({ ...prev, [key]: value }));
    
    // 找到对应的事件名称
    const apiEventName = Object.entries(eventTypeMap).find(
      ([_, uiName]) => uiName === key
    )?.[0];
    
    if (!apiEventName) return;
    
    //  更新事件使能状态
    const motionSensitivity = sensitivityMap[sensitivity as keyof typeof sensitivityMap] ?? 1;
    
    // 构造请求参数
    const eventTriggers = Object.entries(eventTypeMap).map(([apiName, uiName]) => {
      const isCurrentEvent = apiName === apiEventName;
      const isEnabled = isCurrentEvent ? value : detectEvents[uiName as keyof typeof detectEvents];
      
      return {
        name: apiName,
        enabled: isEnabled,
        sensitivity: apiName === "move" ? motionSensitivity : 1
      };
    });
    
    saveConfig({
      camera: [camera.did],
      config: {
        event_trigger: eventTriggers
      }
    });
  };

  useEffect(() => {
    const container = document.getElementById('cameraManagementContainer');
    if (container) {
      const prevBg = container.style.backgroundColor;
      container.style.backgroundColor = 'var(--home-background-color)';
      return () => {
        container.style.backgroundColor = prevBg;
      };
    }
  }, []);

  return (
    <div className={styles.container}>
      <NavigatorBar
        backIcon={isDarkMode ? arrowLeftDark : arrowLeft}
      />
      <div className={styles.title}>检测事件</div>

      <div className={styles.rish}>风险事件</div>

      <List className={styles.eventList}>
        {/* 移动侦测 */}
        <div
          className={styles.eventItemWrapper}
          style={{ marginBottom: 0, borderRadius: "16px 16px 0 0" }}
        >
          <Image src={detect} className={styles.eventIcon} />
          <div className={styles.eventContent}>
            <div className={styles.sectionTitle}>移动侦测</div>
            <div className={styles.eventDesc}>
              AI检测画面中的变化，不同灵敏度将产生不同数量记录
            </div>
          </div>
          <Switch
            checked={detectEvents.motionDetect}
            className={styles.switch}
            onChange={handleSwitchChange("motionDetect")}
          />
        </div>

        {/* 灵敏度设置 - 使用PopoverSelector */}
        <PopoverSelector
          visible={sensitivityPopoverVisible}
          onVisibleChange={setSensitivityPopoverVisible}
          value={sensitivity}
          options={sensitivityOptions}
          onChange={handleSensitivityChange}
        >
          <div>
            <List.Item
              className={styles.eventItem}
              onClick={() => setSensitivityPopoverVisible(true)}
              arrow={false}
            >
              <div className={styles.itemContent}>
                <span className={styles.itemLabel}>灵敏度</span>
                <div className={styles.itemBox}>
                  <span className={styles.itemValue}>{sensitivity}</span>
                  <Image src={isDarkMode ? enterRightDark : enterRight} className={styles.arrow} />
                </div>
              </div>
            </List.Item>
          </div>
        </PopoverSelector>

        {/* 人形检测 */}
        <div className={styles.eventItemWrapper}>
          <Image src={humanoid} className={styles.eventIcon} />
          <div className={styles.eventContent}>
            <div className={styles.sectionTitle}>人形检测</div>
            <div className={styles.eventDesc}>
              AI检测画面中是否有人出现，出现后记录为事件
            </div>
          </div>
          <Switch
            checked={detectEvents.humanDetect}
            className={styles.switch}
            onChange={handleSwitchChange("humanDetect")}
          />
        </div>

        {/* 烟火检测 */}
        <div className={styles.eventItemWrapper}>
          <Image src={fireworks} className={styles.eventIcon} />
          <div className={styles.eventContent}>
            <div className={styles.sectionTitle}>烟火检测</div>
            <div className={styles.eventDesc}>
              AI检测画面中是否有明火出现，帮你及时发现家庭火情风险
            </div>
          </div>
          <Switch
            checked={detectEvents.fireDetect}
            className={styles.switch}
            onChange={handleSwitchChange("fireDetect")}
          />
        </div>

        {/* 宠物检测 */}
        <div className={styles.eventItemWrapper}>
          <Image src={pet} className={styles.eventIcon} />
          <div className={styles.eventContent}>
            <div className={styles.sectionTitle}>宠物检测</div>
            <div className={styles.eventDesc}>
              AI检测画面中是否有宠物出现，让你及时了解萌宠状态
            </div>
          </div>
          <Switch
            checked={detectEvents.petDetect}
            className={styles.switch}
            onChange={handleSwitchChange("petDetect")}
          />
        </div>

        {/* 异响检测 */}
        {/* <div className={styles.eventItemWrapper}>
          <Image src={abnormal} className={styles.eventIcon} />
          <div className={styles.eventContent}>
            <div className={styles.sectionTitle}>异响检测</div>
            <div className={styles.eventDesc}>
              AI检测周围环境是否有较大声音，让你知晓家庭异常
            </div>
          </div>
          <Switch
            checked={detectEvents.noiseDetect}
            className={styles.switch}
            onChange={handleSwitchChange("noiseDetect")}
          />
        </div> */}
      </List>
    </div>
  );
};

export default EventDetection;
