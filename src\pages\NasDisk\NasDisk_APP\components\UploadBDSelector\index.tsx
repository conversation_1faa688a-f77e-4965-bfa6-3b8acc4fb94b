import React, { useState, useEffect, useCallback } from 'react';
import { Popup, Button, Toast, Loading } from 'antd-mobile';
import { RightOutline } from 'antd-mobile-icons';
import styles from './index.module.scss';
import { useTheme } from "@/utils/themeDetector";
import { useRequest } from 'ahooks';
import { getBaiduNetdiskFileList, createBaiduNetdiskFolder } from '@/api/nasDisk';
import { PreloadImage } from '@/components/Image';
import CreateFolder from '../CreateFolder';
import createIcon from "@/Resources/nasDiskImg/create.png";
import createIconDark from "@/Resources/nasDiskImg/create-dark.png";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import file_icon from "@/Resources/nasDiskImg/file-icon.png";


// 内部使用的文件项接口
interface FileItem {
  fs_id: number;
  name: string;
  path: string;
  type: 'folder' | 'file';
  time: string;
  itemCount?: number;
  size: number;
  isdir: boolean;
}

interface UploadBDSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (path: string, displayPath: string) => void;
  title?: string;
}

// 面包屑项目类型
interface BreadcrumbItem {
  id: string;
  name: string;
  path: string;
}

const UploadBDSelector: React.FC<UploadBDSelectorProps> = ({
  visible,
  onClose,
  onSelect,
  title = '更改自动上传位置'
}) => {
  const { isDarkMode } = useTheme();
  
  // 文件列表
  const [fileList, setFileList] = useState<FileItem[]>([]);
  
  // 当前路径面包屑
  const [breadcrumbPath, setBreadcrumbPath] = useState<BreadcrumbItem[]>([
    { id: 'root', name: '百度网盘', path: '/' }
  ]);
  
  // 当前路径
  const [currentPath, setCurrentPath] = useState<string>('/');
  
  // 新建文件夹弹窗状态
  const [showCreateFolderModal, setShowCreateFolderModal] = useState<boolean>(false);

  // 使用 useRequest 获取文件列表
  const { run: fetchFileList, loading } = useRequest(
    (params: { path: string }) => {
      // 调用百度网盘接口
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: params.path,
        order: 'name',
        desc: 1
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response && response.errno === 0) {
          // 只显示文件夹
          const folders: FileItem[] = response.list
            .filter((item: any) => item.isdir === 1)
            .map((item: any) => ({
              fs_id: item.fs_id,
              name: item.server_filename,
              path: item.path,
              type: "folder" as const,
              time: formatTime(item.server_mtime),
              itemCount: Math.floor(Math.random() * 50) + 1, // 模拟项目数量
              size: item.size,
              isdir: true,
            }));

          setFileList(folders);
        } else {
          console.error("获取文件列表失败:", response);
          setFileList([]);
          Toast.show({
            content: '获取文件列表失败，请重试',
            position: 'bottom',
            duration: 2000,
          });
        }
      },
      onError: (error) => {
        console.error("加载文件列表出错:", error);
        setFileList([]);
        Toast.show({
          content: '获取文件列表失败，请重试',
          position: 'bottom',
          duration: 2000,
        });
      },
    }
  );

  // 创建文件夹请求
  const { run: runCreateFolder } = useRequest(
    (params: { path: string }) => {
      return createBaiduNetdiskFolder({
        action: "createdir",
        path: params.path
      });
      },
      {
      manual: true,
      onSuccess: (response) => {
        if (response.errno === 0 || response.code === 0) {
          Toast.show({
            content: "创建文件夹成功",
            position: 'bottom',
            duration: 2000,
          });
          handleCreateFolderSuccess();
        } else {
          Toast.show({
            content: response.errmsg || "创建文件夹失败，请重试",
            position: 'bottom',
            duration: 2000,
          });
        }
      },
      onError: (error) => {
        console.error("创建文件夹失败：", error);
        Toast.show({
          content: "创建文件夹失败，请重试",
          position: 'bottom',
          duration: 2000,
        });
      }
    }
  );

  // 格式化时间戳
  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '/');
  };

  // 初始化时获取根目录
  useEffect(() => {
    if (visible) {
      fetchFileList({ path: '/' });
      // 重置状态
      setCurrentPath('/');
      setBreadcrumbPath([{ id: 'root', name: '百度网盘', path: '/' }]);
    }
  }, [visible, fetchFileList]);

  // 处理文件夹点击（直接进入文件夹）
  const handleFolderClick = useCallback((folder: FileItem) => {
    // 构建新路径
    const newPath = currentPath === '/' ? `/${folder.name}` : `${currentPath}/${folder.name}`;
    
    // 更新导航和路径
    setCurrentPath(newPath);
    const newBreadcrumb: BreadcrumbItem = {
      id: `folder_${folder.fs_id}`,
      name: folder.name,
      path: newPath
    };
    setBreadcrumbPath(prev => [...prev, newBreadcrumb]);
    
    // 获取子文件夹内容
    fetchFileList({ path: newPath });
  }, [currentPath, fetchFileList]);

  // 通过面包屑导航到指定路径
  const navigateToBreadcrumb = useCallback((index: number) => {
    const newPath = breadcrumbPath.slice(0, index + 1);
    setBreadcrumbPath(newPath);
    
    const targetPath = newPath[newPath.length - 1].path;
    setCurrentPath(targetPath);
    
    // 获取该路径下的内容
    fetchFileList({ path: targetPath });
  }, [breadcrumbPath, fetchFileList]);

  // 处理新建文件夹
  const handleCreateFolder = () => {
    setShowCreateFolderModal(true);
  };

  // 处理创建文件夹成功
  const handleCreateFolderSuccess = () => {
    setShowCreateFolderModal(false);
    // 刷新当前目录
    fetchFileList({ path: currentPath });
  };

  // 处理创建文件夹取消
  const handleCreateFolderCancel = () => {
    setShowCreateFolderModal(false);
  };

  // 创建新文件夹
  const createFolder = (folderName?: string) => {
    if (!folderName) {
      Toast.show({
        content: "请输入文件夹名称",
        position: 'bottom',
        duration: 2000,
      });
      return;
    }
    
    // 构建完整路径
    const newFolderPath = currentPath === '/' 
      ? `/${folderName}` 
      : `${currentPath}/${folderName}`;
    
    // 调用创建文件夹接口
    runCreateFolder({ path: newFolderPath });
  };

  // 选择当前路径
  const handleSelect = useCallback(() => {
    // 使用当前路径作为选择路径
    const uploadPath = currentPath;
    const displayPath = breadcrumbPath.map(item => item.name).join(' > ');
    
    onSelect(uploadPath, displayPath);
    onClose();
    
    // 重置状态
    setCurrentPath('/');
    setBreadcrumbPath([{ id: 'root', name: '百度网盘', path: '/' }]);
    setFileList([]);
  }, [currentPath, breadcrumbPath, onSelect, onClose]);

  // 关闭弹窗时重置状态
  const handleClose = useCallback(() => {
    setCurrentPath('/');
    setBreadcrumbPath([{ id: 'root', name: '百度网盘', path: '/' }]);
    setFileList([]);
    onClose();
  }, [onClose]);

  return (
    <Popup
      visible={visible}
      onMaskClick={handleClose}
      bodyStyle={{
        borderTopLeftRadius: '30px',
        borderTopRightRadius: '30px',
        height:'95%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div className={styles.container}>
        {/* 头部 */}
        <div className={styles.header}>
          <PreloadImage    src={isDarkMode ? arrowLeftDark : arrowLeft} alt='close' style={{ width: '40px', height: '40px' }} onClick={handleClose} />
          <span className={styles.title}>{title}</span>
          <div className={styles.rightSection}>
              <PreloadImage
              style={{width: '40px', height: '40px'}}
                src={isDarkMode ? createIconDark : createIcon}
                alt="新建文件夹"
                className={styles.createButton}
                onClick={handleCreateFolder}
              />
            </div>
        </div>
        
        {/* 面包屑 */}
        <div className={styles.breadcrumb}>
          {breadcrumbPath.map((pathItem, index) => (
            <React.Fragment key={pathItem.id}>
              <span 
                className={`${styles.breadcrumbItem} ${index === breadcrumbPath.length - 1 ? styles.active : ''}`}
                onClick={() => navigateToBreadcrumb(index)}
              >
                {pathItem.name}
              </span>
              {index < breadcrumbPath.length - 1 && (
                <RightOutline className={styles.breadcrumbSeparator} />
              )}
            </React.Fragment>
          ))}
        </div>
        
        {/* 加载状态 */}
        {loading && (
          <div className={styles.loadingContainer}>
            <Loading />
            <span>加载中...</span>
          </div>
        )}
        
        {/* 文件列表 */}
        <div className={styles.fileList}>
          {fileList.map(folder => (
            <div 
              key={folder.fs_id}
              className={styles.fileItem}
              onClick={() => handleFolderClick(folder)}
            >
              <div className={styles.fileIcon}>
                <PreloadImage    src={file_icon} alt='close' style={{ width: '40px', height: '40px' }}  />
              </div>
              <div className={styles.fileInfo}>
                <div className={styles.fileName}>
                  {folder.name}
                </div>
                <div className={styles.fileTime}>{folder.time}</div>
                {/* {folder.itemCount !== undefined && (
                  <div className={styles.itemCount}>{folder.itemCount}个项目</div>
                )} */}
              </div>
              <div className={styles.rightArrow}>
                 <RightOutline />
              </div>
            </div>
          ))}
          
          {!loading && fileList.length === 0 && (
            <div className={styles.emptyState}>
              <span>该目录下没有文件夹</span>
            </div>
          )}
        </div>
        
        {/* 底部按钮 */}
        <div className={styles.footer}>
          {/* <Button 
            className={styles.createButton}
            onClick={handleCreateFolder}
            color='default'
            size='large'
          >
            + 新建文件夹
          </Button> */}
          <Button 
            className={styles.selectButton}
            onClick={handleSelect}
            color='primary'
            size='large'
          >
            更改到当前位置
          </Button>
        </div>
      </div>

      {/* 新建文件夹弹窗 */}
      <CreateFolder
        visible={showCreateFolderModal}
        onCancel={handleCreateFolderCancel}
        onSuccess={createFolder}
        currentPath={currentPath}
      />
    </Popup>
  );
};

export default UploadBDSelector;
